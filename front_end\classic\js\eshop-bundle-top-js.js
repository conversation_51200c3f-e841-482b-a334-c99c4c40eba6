!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.moment=e()}(this,function(){"use strict";var t;function m(){return t.apply(null,arguments)}function r(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function o(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function n(t){return void 0===t}function l(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function h(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function d(t,e){for(var a=[],i=0;i<t.length;++i)a.push(e(t[i],i));return a}function f(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function c(t,e){for(var a in e)f(e,a)&&(t[a]=e[a]);return f(e,"toString")&&(t.toString=e.toString),f(e,"valueOf")&&(t.valueOf=e.valueOf),t}function u(t,e,a,i){return Ye(t,e,a,i,!0).utc()}function p(t){return null==t._pf&&(t._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null,rfc2822:!1,weekdayMismatch:!1}),t._pf}function g(t){if(null==t._isValid){var e=p(t),a=i.call(e.parsedDateParts,function(t){return null!=t}),a=!isNaN(t._d.getTime())&&e.overflow<0&&!e.empty&&!e.invalidMonth&&!e.invalidWeekday&&!e.weekdayMismatch&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&a);if(t._strict&&(a=a&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour),null!=Object.isFrozen&&Object.isFrozen(t))return a;t._isValid=a}return t._isValid}function y(t){var e=u(NaN);return null!=t?c(p(e),t):p(e).userInvalidated=!0,e}var i=Array.prototype.some||function(t){for(var e=Object(this),a=e.length>>>0,i=0;i<a;i++)if(i in e&&t.call(this,e[i],i,e))return!0;return!1},_=m.momentProperties=[];function D(t,e){var a,i,s;if(n(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),n(e._i)||(t._i=e._i),n(e._f)||(t._f=e._f),n(e._l)||(t._l=e._l),n(e._strict)||(t._strict=e._strict),n(e._tzm)||(t._tzm=e._tzm),n(e._isUTC)||(t._isUTC=e._isUTC),n(e._offset)||(t._offset=e._offset),n(e._pf)||(t._pf=p(e)),n(e._locale)||(t._locale=e._locale),0<_.length)for(a=0;a<_.length;a++)n(s=e[i=_[a]])||(t[i]=s);return t}var e=!1;function v(t){D(this,t),this._d=new Date(null!=t._d?t._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===e&&(e=!0,m.updateOffset(this),e=!1)}function k(t){return t instanceof v||null!=t&&null!=t._isAMomentObject}function w(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function C(t){var e=+t,t=0;return t=0!=e&&isFinite(e)?w(e):t}function M(t,e,a){for(var i=Math.min(t.length,e.length),s=Math.abs(t.length-e.length),n=0,r=0;r<i;r++)(a&&t[r]!==e[r]||!a&&C(t[r])!==C(e[r]))&&n++;return n+s}function Y(t){!1===m.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+t)}function a(s,n){var r=!0;return c(function(){if(null!=m.deprecationHandler&&m.deprecationHandler(null,s),r){for(var t,e=[],a=0;a<arguments.length;a++){if(t="","object"==typeof arguments[a]){for(var i in t+="\n["+a+"] ",arguments[0])t+=i+": "+arguments[0][i]+", ";t=t.slice(0,-2)}else t=arguments[a];e.push(t)}Y(s+"\nArguments: "+Array.prototype.slice.call(e).join("")+"\n"+(new Error).stack),r=!1}return n.apply(this,arguments)},n)}var s={};function b(t,e){null!=m.deprecationHandler&&m.deprecationHandler(t,e),s[t]||(Y(e),s[t]=!0)}function S(t){return t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function x(t,e){var a,i=c({},t);for(a in e)f(e,a)&&(o(t[a])&&o(e[a])?(i[a]={},c(i[a],t[a]),c(i[a],e[a])):null!=e[a]?i[a]=e[a]:delete i[a]);for(a in t)f(t,a)&&!f(e,a)&&o(t[a])&&(i[a]=c({},i[a]));return i}function P(t){null!=t&&this.set(t)}m.suppressDeprecationWarnings=!1,m.deprecationHandler=null;var T=Object.keys||function(t){var e,a=[];for(e in t)f(t,e)&&a.push(e);return a},O={};function W(t,e){var a=t.toLowerCase();O[a]=O[a+"s"]=O[e]=t}function E(t){return"string"==typeof t?O[t]||O[t.toLowerCase()]:void 0}function L(t){var e,a,i={};for(a in t)f(t,a)&&(e=E(a))&&(i[e]=t[a]);return i}var N={};function A(t,e){N[t]=e}function I(t,e,a){var i=""+Math.abs(t);return(0<=t?a?"+":"":"-")+Math.pow(10,Math.max(0,e-i.length)).toString().substr(1)+i}var H=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,F=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,R={},V={};function U(t,e,a,i){var s="string"==typeof i?function(){return this[i]()}:i;t&&(V[t]=s),e&&(V[e[0]]=function(){return I(s.apply(this,arguments),e[1],e[2])}),a&&(V[a]=function(){return this.localeData().ordinal(s.apply(this,arguments),t)})}function j(t,e){return t.isValid()?(e=$(e,t.localeData()),R[e]=R[e]||function(i){for(var t,s=i.match(H),e=0,n=s.length;e<n;e++)V[s[e]]?s[e]=V[s[e]]:s[e]=(t=s[e]).match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"");return function(t){for(var e="",a=0;a<n;a++)e+=S(s[a])?s[a].call(t,i):s[a];return e}}(e),R[e](t)):t.localeData().invalidDate()}function $(t,e){var a=5;function i(t){return e.longDateFormat(t)||t}for(F.lastIndex=0;0<=a&&F.test(t);)t=t.replace(F,i),F.lastIndex=0,--a;return t}var B=/\d/,G=/\d\d/,z=/\d{3}/,Z=/\d{4}/,q=/[+-]?\d{6}/,Q=/\d\d?/,J=/\d\d\d\d?/,X=/\d\d\d\d\d\d?/,K=/\d{1,3}/,tt=/\d{1,4}/,et=/[+-]?\d{1,6}/,at=/\d+/,it=/[+-]?\d+/,st=/Z|[+-]\d\d:?\d\d/gi,nt=/Z|[+-]\d\d(?::?\d\d)?/gi,rt=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,ot={};function lt(t,a,i){ot[t]=S(a)?a:function(t,e){return t&&i?i:a}}function ht(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var dt={};function ct(t,a){var e,i=a;for("string"==typeof t&&(t=[t]),l(a)&&(i=function(t,e){e[a]=C(t)}),e=0;e<t.length;e++)dt[t[e]]=i}function ut(t,s){ct(t,function(t,e,a,i){a._w=a._w||{},s(t,a._w,a,i)})}var ft=0,mt=1,pt=2,gt=3,yt=4,_t=5,Dt=6,vt=7,kt=8;function wt(t){return Ct(t)?366:365}function Ct(t){return t%4==0&&t%100!=0||t%400==0}U("Y",0,0,function(){var t=this.year();return t<=9999?""+t:"+"+t}),U(0,["YY",2],0,function(){return this.year()%100}),U(0,["YYYY",4],0,"year"),U(0,["YYYYY",5],0,"year"),U(0,["YYYYYY",6,!0],0,"year"),W("year","y"),A("year",1),lt("Y",it),lt("YY",Q,G),lt("YYYY",tt,Z),lt("YYYYY",et,q),lt("YYYYYY",et,q),ct(["YYYYY","YYYYYY"],ft),ct("YYYY",function(t,e){e[ft]=2===t.length?m.parseTwoDigitYear(t):C(t)}),ct("YY",function(t,e){e[ft]=m.parseTwoDigitYear(t)}),ct("Y",function(t,e){e[ft]=parseInt(t,10)}),m.parseTwoDigitYear=function(t){return C(t)+(68<C(t)?1900:2e3)};var Mt,Yt=bt("FullYear",!0);function bt(e,a){return function(t){return null!=t?(xt(this,e,t),m.updateOffset(this,a),this):St(this,e)}}function St(t,e){return t.isValid()?t._d["get"+(t._isUTC?"UTC":"")+e]():NaN}function xt(t,e,a){t.isValid()&&!isNaN(a)&&("FullYear"===e&&Ct(t.year())&&1===t.month()&&29===t.date()?t._d["set"+(t._isUTC?"UTC":"")+e](a,t.month(),Pt(a,t.month())):t._d["set"+(t._isUTC?"UTC":"")+e](a))}function Pt(t,e){if(isNaN(t)||isNaN(e))return NaN;var a=(e%12+12)%12;return t+=(e-a)/12,1==a?Ct(t)?29:28:31-a%7%2}Mt=Array.prototype.indexOf||function(t){for(var e=0;e<this.length;++e)if(this[e]===t)return e;return-1},U("M",["MM",2],"Mo",function(){return this.month()+1}),U("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),U("MMMM",0,0,function(t){return this.localeData().months(this,t)}),W("month","M"),A("month",8),lt("M",Q),lt("MM",Q,G),lt("MMM",function(t,e){return e.monthsShortRegex(t)}),lt("MMMM",function(t,e){return e.monthsRegex(t)}),ct(["M","MM"],function(t,e){e[mt]=C(t)-1}),ct(["MMM","MMMM"],function(t,e,a,i){i=a._locale.monthsParse(t,i,a._strict);null!=i?e[mt]=i:p(a).invalidMonth=t});var Tt=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ot="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Wt="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");function Et(t,e){var a;if(!t.isValid())return t;if("string"==typeof e)if(/^\d+$/.test(e))e=C(e);else if(!l(e=t.localeData().monthsParse(e)))return t;return a=Math.min(t.date(),Pt(t.year(),e)),t._d["set"+(t._isUTC?"UTC":"")+"Month"](e,a),t}function Lt(t){return null!=t?(Et(this,t),m.updateOffset(this,!0),this):St(this,"Month")}var Nt=rt,At=rt;function It(){function t(t,e){return e.length-t.length}for(var e,a=[],i=[],s=[],n=0;n<12;n++)e=u([2e3,n]),a.push(this.monthsShort(e,"")),i.push(this.months(e,"")),s.push(this.months(e,"")),s.push(this.monthsShort(e,""));for(a.sort(t),i.sort(t),s.sort(t),n=0;n<12;n++)a[n]=ht(a[n]),i[n]=ht(i[n]);for(n=0;n<24;n++)s[n]=ht(s[n]);this._monthsRegex=new RegExp("^("+s.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+a.join("|")+")","i")}function Ht(t){var e;return t<100&&0<=t?((e=Array.prototype.slice.call(arguments))[0]=t+400,e=new Date(Date.UTC.apply(null,e)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)):e=new Date(Date.UTC.apply(null,arguments)),e}function Ft(t,e,a){a=7+e-a;return-(7+Ht(t,0,a).getUTCDay()-e)%7+a-1}function Rt(t,e,a,i,s){var n,s=1+7*(e-1)+(7+a-i)%7+Ft(t,i,s),s=s<=0?wt(n=t-1)+s:s>wt(t)?(n=t+1,s-wt(t)):(n=t,s);return{year:n,dayOfYear:s}}function Vt(t,e,a){var i,s,n=Ft(t.year(),e,a),n=Math.floor((t.dayOfYear()-n-1)/7)+1;return n<1?i=n+Ut(s=t.year()-1,e,a):n>Ut(t.year(),e,a)?(i=n-Ut(t.year(),e,a),s=t.year()+1):(s=t.year(),i=n),{week:i,year:s}}function Ut(t,e,a){var i=Ft(t,e,a),a=Ft(t+1,e,a);return(wt(t)-i+a)/7}function jt(t,e){return t.slice(e,7).concat(t.slice(0,e))}U("w",["ww",2],"wo","week"),U("W",["WW",2],"Wo","isoWeek"),W("week","w"),W("isoWeek","W"),A("week",5),A("isoWeek",5),lt("w",Q),lt("ww",Q,G),lt("W",Q),lt("WW",Q,G),ut(["w","ww","W","WW"],function(t,e,a,i){e[i.substr(0,1)]=C(t)}),U("d",0,"do","day"),U("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),U("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),U("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),U("e",0,0,"weekday"),U("E",0,0,"isoWeekday"),W("day","d"),W("weekday","e"),W("isoWeekday","E"),A("day",11),A("weekday",11),A("isoWeekday",11),lt("d",Q),lt("e",Q),lt("E",Q),lt("dd",function(t,e){return e.weekdaysMinRegex(t)}),lt("ddd",function(t,e){return e.weekdaysShortRegex(t)}),lt("dddd",function(t,e){return e.weekdaysRegex(t)}),ut(["dd","ddd","dddd"],function(t,e,a,i){i=a._locale.weekdaysParse(t,i,a._strict);null!=i?e.d=i:p(a).invalidWeekday=t}),ut(["d","e","E"],function(t,e,a,i){e[i]=C(t)});var $t="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Bt="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Gt="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),zt=rt,Zt=rt,qt=rt;function Qt(){function t(t,e){return e.length-t.length}for(var e,a,i,s=[],n=[],r=[],o=[],l=0;l<7;l++)i=u([2e3,1]).day(l),e=this.weekdaysMin(i,""),a=this.weekdaysShort(i,""),i=this.weekdays(i,""),s.push(e),n.push(a),r.push(i),o.push(e),o.push(a),o.push(i);for(s.sort(t),n.sort(t),r.sort(t),o.sort(t),l=0;l<7;l++)n[l]=ht(n[l]),r[l]=ht(r[l]),o[l]=ht(o[l]);this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+n.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+s.join("|")+")","i")}function Jt(){return this.hours()%12||12}function Xt(t,e){U(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function Kt(t,e){return e._meridiemParse}U("H",["HH",2],0,"hour"),U("h",["hh",2],0,Jt),U("k",["kk",2],0,function(){return this.hours()||24}),U("hmm",0,0,function(){return""+Jt.apply(this)+I(this.minutes(),2)}),U("hmmss",0,0,function(){return""+Jt.apply(this)+I(this.minutes(),2)+I(this.seconds(),2)}),U("Hmm",0,0,function(){return""+this.hours()+I(this.minutes(),2)}),U("Hmmss",0,0,function(){return""+this.hours()+I(this.minutes(),2)+I(this.seconds(),2)}),Xt("a",!0),Xt("A",!1),W("hour","h"),A("hour",13),lt("a",Kt),lt("A",Kt),lt("H",Q),lt("h",Q),lt("k",Q),lt("HH",Q,G),lt("hh",Q,G),lt("kk",Q,G),lt("hmm",J),lt("hmmss",X),lt("Hmm",J),lt("Hmmss",X),ct(["H","HH"],gt),ct(["k","kk"],function(t,e,a){t=C(t);e[gt]=24===t?0:t}),ct(["a","A"],function(t,e,a){a._isPm=a._locale.isPM(t),a._meridiem=t}),ct(["h","hh"],function(t,e,a){e[gt]=C(t),p(a).bigHour=!0}),ct("hmm",function(t,e,a){var i=t.length-2;e[gt]=C(t.substr(0,i)),e[yt]=C(t.substr(i)),p(a).bigHour=!0}),ct("hmmss",function(t,e,a){var i=t.length-4,s=t.length-2;e[gt]=C(t.substr(0,i)),e[yt]=C(t.substr(i,2)),e[_t]=C(t.substr(s)),p(a).bigHour=!0}),ct("Hmm",function(t,e,a){var i=t.length-2;e[gt]=C(t.substr(0,i)),e[yt]=C(t.substr(i))}),ct("Hmmss",function(t,e,a){var i=t.length-4,s=t.length-2;e[gt]=C(t.substr(0,i)),e[yt]=C(t.substr(i,2)),e[_t]=C(t.substr(s))});var te,ee=bt("Hours",!0),ae={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Ot,monthsShort:Wt,week:{dow:0,doy:6},weekdays:$t,weekdaysMin:Gt,weekdaysShort:Bt,meridiemParse:/[ap]\.?m?\.?/i},ie={},se={};function ne(t){return t&&t.toLowerCase().replace("_","-")}function re(t){var e;if(!ie[t]&&"undefined"!=typeof module&&module&&module.exports)try{e=te._abbr,require("./locale/"+t),oe(e)}catch(t){}return ie[t]}function oe(t,e){return t&&((e=n(e)?he(t):le(t,e))?te=e:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),te._abbr}function le(t,e){if(null===e)return delete ie[t],null;var a,i=ae;if(e.abbr=t,null!=ie[t])b("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),i=ie[t]._config;else if(null!=e.parentLocale)if(null!=ie[e.parentLocale])i=ie[e.parentLocale]._config;else{if(null==(a=re(e.parentLocale)))return se[e.parentLocale]||(se[e.parentLocale]=[]),se[e.parentLocale].push({name:t,config:e}),null;i=a._config}return ie[t]=new P(x(i,e)),se[t]&&se[t].forEach(function(t){le(t.name,t.config)}),oe(t),ie[t]}function he(t){var e;if(!(t=t&&t._locale&&t._locale._abbr?t._locale._abbr:t))return te;if(!r(t)){if(e=re(t))return e;t=[t]}return function(t){for(var e,a,i,s,n=0;n<t.length;){for(e=(s=ne(t[n]).split("-")).length,a=(a=ne(t[n+1]))?a.split("-"):null;0<e;){if(i=re(s.slice(0,e).join("-")))return i;if(a&&a.length>=e&&M(s,a,!0)>=e-1)break;e--}n++}return te}(t)}function de(t){var e=t._a;return e&&-2===p(t).overflow&&(e=e[mt]<0||11<e[mt]?mt:e[pt]<1||e[pt]>Pt(e[ft],e[mt])?pt:e[gt]<0||24<e[gt]||24===e[gt]&&(0!==e[yt]||0!==e[_t]||0!==e[Dt])?gt:e[yt]<0||59<e[yt]?yt:e[_t]<0||59<e[_t]?_t:e[Dt]<0||999<e[Dt]?Dt:-1,p(t)._overflowDayOfYear&&(e<ft||pt<e)&&(e=pt),p(t)._overflowWeeks&&-1===e&&(e=vt),p(t)._overflowWeekday&&-1===e&&(e=kt),p(t).overflow=e),t}function ce(t,e,a){return null!=t?t:null!=e?e:a}function ue(t){var e,a,i,s,n,r,o,l,h,d=[];if(!t._d){var c=t,u=new Date(m.now()),f=c._useUTC?[u.getUTCFullYear(),u.getUTCMonth(),u.getUTCDate()]:[u.getFullYear(),u.getMonth(),u.getDate()];for(t._w&&null==t._a[pt]&&null==t._a[mt]&&(null!=(c=(i=t)._w).GG||null!=c.W||null!=c.E?(r=1,o=4,l=ce(c.GG,i._a[ft],Vt(be(),1,4).year),h=ce(c.W,1),((s=ce(c.E,1))<1||7<s)&&(n=!0)):(r=i._locale._week.dow,o=i._locale._week.doy,u=Vt(be(),r,o),l=ce(c.gg,i._a[ft],u.year),h=ce(c.w,u.week),null!=c.d?((s=c.d)<0||6<s)&&(n=!0):null!=c.e?(s=c.e+r,(c.e<0||6<c.e)&&(n=!0)):s=r),h<1||h>Ut(l,r,o)?p(i)._overflowWeeks=!0:null!=n?p(i)._overflowWeekday=!0:(o=Rt(l,h,s,r,o),i._a[ft]=o.year,i._dayOfYear=o.dayOfYear)),null!=t._dayOfYear&&(a=ce(t._a[ft],f[ft]),(t._dayOfYear>wt(a)||0===t._dayOfYear)&&(p(t)._overflowDayOfYear=!0),a=Ht(a,0,t._dayOfYear),t._a[mt]=a.getUTCMonth(),t._a[pt]=a.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=d[e]=f[e];for(;e<7;e++)t._a[e]=d[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[gt]&&0===t._a[yt]&&0===t._a[_t]&&0===t._a[Dt]&&(t._nextDay=!0,t._a[gt]=0),t._d=(t._useUTC?Ht:function(t,e,a,i,s,n,r){var o;return t<100&&0<=t?(o=new Date(t+400,e,a,i,s,n,r),isFinite(o.getFullYear())&&o.setFullYear(t)):o=new Date(t,e,a,i,s,n,r),o}).apply(null,d),a=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[gt]=24),t._w&&void 0!==t._w.d&&t._w.d!==a&&(p(t).weekdayMismatch=!0)}}var fe=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,me=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pe=/Z|[+-]\d\d(?::?\d\d)?/,ge=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],ye=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],_e=/^\/?Date\((\-?\d+)/i;function De(t){var e,a,i,s,n,r,o=t._i,l=fe.exec(o)||me.exec(o);if(l){for(p(t).iso=!0,e=0,a=ge.length;e<a;e++)if(ge[e][1].exec(l[1])){s=ge[e][0],i=!1!==ge[e][2];break}if(null==s)return t._isValid=!1;if(l[3]){for(e=0,a=ye.length;e<a;e++)if(ye[e][1].exec(l[3])){n=(l[2]||" ")+ye[e][0];break}if(null==n)return t._isValid=!1}if(!i&&null!=n)return t._isValid=!1;if(l[4]){if(!pe.exec(l[4]))return t._isValid=!1;r="Z"}t._f=s+(n||"")+(r||""),Ce(t)}else t._isValid=!1}var ve=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/;var ke={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function we(t){var e,a,i,s,n,r,o=ve.exec(t._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));o?(e=o[4],a=o[3],i=o[2],s=o[5],n=o[6],r=o[7],n=[(e=parseInt(e,10))<=49?2e3+e:e<=999?1900+e:e,Wt.indexOf(a),parseInt(i,10),parseInt(s,10),parseInt(n,10)],r&&n.push(parseInt(r,10)),s=i=n,r=t,(n=o[1])&&Bt.indexOf(n)!==new Date(s[0],s[1],s[2]).getDay()?(p(r).weekdayMismatch=!0,r._isValid=!1):(t._a=i,t._tzm=function(t,e,a){if(t)return ke[t];if(e)return 0;e=parseInt(a,10),a=e%100;return(e-a)/100*60+a}(o[8],o[9],o[10]),t._d=Ht.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),p(t).rfc2822=!0)):t._isValid=!1}function Ce(t){if(t._f!==m.ISO_8601)if(t._f!==m.RFC_2822){t._a=[],p(t).empty=!0;for(var e,a,i,s=""+t._i,n=s.length,r=0,o=$(t._f,t._locale).match(H)||[],l=0;l<o.length;l++)a=o[l],(e=(s.match((u=t,f(ot,c=a)?ot[c](u._strict,u._locale):new RegExp(ht(c.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,a,i,s){return e||a||i||s})))))||[])[0])&&(0<(i=s.substr(0,s.indexOf(e))).length&&p(t).unusedInput.push(i),s=s.slice(s.indexOf(e)+e.length),r+=e.length),V[a]?(e?p(t).empty=!1:p(t).unusedTokens.push(a),u=a,c=t,null!=(i=e)&&f(dt,u)&&dt[u](i,c._a,c,u)):t._strict&&!e&&p(t).unusedTokens.push(a);p(t).charsLeftOver=n-r,0<s.length&&p(t).unusedInput.push(s),t._a[gt]<=12&&!0===p(t).bigHour&&0<t._a[gt]&&(p(t).bigHour=void 0),p(t).parsedDateParts=t._a.slice(0),p(t).meridiem=t._meridiem,t._a[gt]=(h=t._locale,d=t._a[gt],null==(n=t._meridiem)?d:null!=h.meridiemHour?h.meridiemHour(d,n):(null!=h.isPM&&((n=h.isPM(n))&&d<12&&(d+=12),n||12!==d||(d=0)),d)),ue(t),de(t)}else we(t);else De(t);var h,d,c,u}function Me(t){var e,a,i=t._i,s=t._f;return t._locale=t._locale||he(t._l),null===i||void 0===s&&""===i?y({nullInput:!0}):("string"==typeof i&&(t._i=i=t._locale.preparse(i)),k(i)?new v(de(i)):(h(i)?t._d=i:r(s)?function(t){var e,a,i,s,n;if(0===t._f.length)return p(t).invalidFormat=!0,t._d=new Date(NaN);for(s=0;s<t._f.length;s++)n=0,e=D({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[s],Ce(e),g(e)&&(n+=p(e).charsLeftOver,n+=10*p(e).unusedTokens.length,p(e).score=n,(null==i||n<i)&&(i=n,a=e));c(t,a||e)}(t):s?Ce(t):n(s=(i=t)._i)?i._d=new Date(m.now()):h(s)?i._d=new Date(s.valueOf()):"string"==typeof s?(e=i,null===(a=_e.exec(e._i))?(De(e),!1===e._isValid&&(delete e._isValid,we(e),!1===e._isValid&&(delete e._isValid,m.createFromInputFallback(e)))):e._d=new Date(+a[1])):r(s)?(i._a=d(s.slice(0),function(t){return parseInt(t,10)}),ue(i)):o(s)?(e=i)._d||(a=L(e._i),e._a=d([a.year,a.month,a.day||a.date,a.hour,a.minute,a.second,a.millisecond],function(t){return t&&parseInt(t,10)}),ue(e)):l(s)?i._d=new Date(s):m.createFromInputFallback(i),g(t)||(t._d=null),t))}function Ye(t,e,a,i,s){var n={};return!0!==a&&!1!==a||(i=a,a=void 0),(o(t)&&function(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;for(var e in t)if(t.hasOwnProperty(e))return;return 1}(t)||r(t)&&0===t.length)&&(t=void 0),n._isAMomentObject=!0,n._useUTC=n._isUTC=s,n._l=a,n._i=t,n._f=e,n._strict=i,(n=new v(de(Me(n))))._nextDay&&(n.add(1,"d"),n._nextDay=void 0),n}function be(t,e,a,i){return Ye(t,e,a,i,!1)}m.createFromInputFallback=a("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),m.ISO_8601=function(){},m.RFC_2822=function(){};var Se=a("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=be.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:y()}),xe=a("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=be.apply(null,arguments);return this.isValid()&&t.isValid()?this<t?this:t:y()});function Pe(t,e){var a,i;if(!(e=1===e.length&&r(e[0])?e[0]:e).length)return be();for(a=e[0],i=1;i<e.length;++i)e[i].isValid()&&!e[i][t](a)||(a=e[i]);return a}var Te=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Oe(t){var e=L(t),a=e.year||0,i=e.quarter||0,s=e.month||0,n=e.week||e.isoWeek||0,r=e.day||0,o=e.hour||0,l=e.minute||0,h=e.second||0,t=e.millisecond||0;this._isValid=function(t){for(var e in t)if(-1===Mt.call(Te,e)||null!=t[e]&&isNaN(t[e]))return!1;for(var a=!1,i=0;i<Te.length;++i)if(t[Te[i]]){if(a)return!1;parseFloat(t[Te[i]])!==C(t[Te[i]])&&(a=!0)}return!0}(e),this._milliseconds=+t+1e3*h+6e4*l+1e3*o*60*60,this._days=+r+7*n,this._months=+s+3*i+12*a,this._data={},this._locale=he(),this._bubble()}function We(t){return t instanceof Oe}function Ee(t){return t<0?-1*Math.round(-1*t):Math.round(t)}function Le(t,a){U(t,0,0,function(){var t=this.utcOffset(),e="+";return t<0&&(t=-t,e="-"),e+I(~~(t/60),2)+a+I(~~t%60,2)})}Le("Z",":"),Le("ZZ",""),lt("Z",nt),lt("ZZ",nt),ct(["Z","ZZ"],function(t,e,a){a._useUTC=!0,a._tzm=Ae(nt,t)});var Ne=/([\+\-]|\d\d)/gi;function Ae(t,e){e=(e||"").match(t);if(null===e)return null;t=((e[e.length-1]||[])+"").match(Ne)||["-",0,0],e=60*t[1]+C(t[2]);return 0===e?0:"+"===t[0]?e:-e}function Ie(t,e){var a;return e._isUTC?(a=e.clone(),e=(k(t)||h(t)?t:be(t)).valueOf()-a.valueOf(),a._d.setTime(a._d.valueOf()+e),m.updateOffset(a,!1),a):be(t).local()}function He(t){return 15*-Math.round(t._d.getTimezoneOffset()/15)}function Fe(){return!!this.isValid()&&this._isUTC&&0===this._offset}m.updateOffset=function(){};var Re=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,Ve=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Ue(t,e){var a,i,s=t,n=null;return We(t)?s={ms:t._milliseconds,d:t._days,M:t._months}:l(t)?(s={},e?s[e]=t:s.milliseconds=t):(n=Re.exec(t))?(a="-"===n[1]?-1:1,s={y:0,d:C(n[pt])*a,h:C(n[gt])*a,m:C(n[yt])*a,s:C(n[_t])*a,ms:C(Ee(1e3*n[Dt]))*a}):(n=Ve.exec(t))?(a="-"===n[1]?-1:1,s={y:je(n[2],a),M:je(n[3],a),w:je(n[4],a),d:je(n[5],a),h:je(n[6],a),m:je(n[7],a),s:je(n[8],a)}):null==s?s={}:"object"==typeof s&&("from"in s||"to"in s)&&(n=be(s.from),a=be(s.to),i=n.isValid()&&a.isValid()?(a=Ie(a,n),n.isBefore(a)?i=$e(n,a):((i=$e(a,n)).milliseconds=-i.milliseconds,i.months=-i.months),i):{milliseconds:0,months:0},(s={}).ms=i.milliseconds,s.M=i.months),s=new Oe(s),We(t)&&f(t,"_locale")&&(s._locale=t._locale),s}function je(t,e){t=t&&parseFloat(t.replace(",","."));return(isNaN(t)?0:t)*e}function $e(t,e){var a={};return a.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(a.months,"M").isAfter(e)&&--a.months,a.milliseconds=+e-+t.clone().add(a.months,"M"),a}function Be(i,s){return function(t,e){var a;return null===e||isNaN(+e)||(b(s,"moment()."+s+"(period, number) is deprecated. Please use moment()."+s+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),a=t,t=e,e=a),Ge(this,Ue(t="string"==typeof t?+t:t,e),i),this}}function Ge(t,e,a,i){var s=e._milliseconds,n=Ee(e._days),e=Ee(e._months);t.isValid()&&(i=null==i||i,e&&Et(t,St(t,"Month")+e*a),n&&xt(t,"Date",St(t,"Date")+n*a),s&&t._d.setTime(t._d.valueOf()+s*a),i&&m.updateOffset(t,n||e))}Ue.fn=Oe.prototype,Ue.invalid=function(){return Ue(NaN)};var ze=Be(1,"add"),rt=Be(-1,"subtract");function Ze(t,e){var a=12*(e.year()-t.year())+(e.month()-t.month()),i=t.clone().add(a,"months");return-(a+(e-i<0?(e-i)/(i-t.clone().add(a-1,"months")):(e-i)/(t.clone().add(1+a,"months")-i)))||0}function qe(t){return void 0===t?this._locale._abbr:(null!=(t=he(t))&&(this._locale=t),this)}m.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",m.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";J=a("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)});function Qe(){return this._locale}var Je=126227808e5;function Xe(t,e){return(t%e+e)%e}function Ke(t,e,a){return t<100&&0<=t?new Date(t+400,e,a)-Je:new Date(t,e,a).valueOf()}function ta(t,e,a){return t<100&&0<=t?Date.UTC(t+400,e,a)-Je:Date.UTC(t,e,a)}function ea(t,e){U(0,[t,t.length],0,e)}function aa(t,e,a,i,s){var n;return null==t?Vt(this,i,s).year:((n=Ut(t,i,s))<e&&(e=n),function(t,e,a,i,s){s=Rt(t,e,a,i,s),s=Ht(s.year,0,s.dayOfYear);return this.year(s.getUTCFullYear()),this.month(s.getUTCMonth()),this.date(s.getUTCDate()),this}.call(this,t,e,a,i,s))}U(0,["gg",2],0,function(){return this.weekYear()%100}),U(0,["GG",2],0,function(){return this.isoWeekYear()%100}),ea("gggg","weekYear"),ea("ggggg","weekYear"),ea("GGGG","isoWeekYear"),ea("GGGGG","isoWeekYear"),W("weekYear","gg"),W("isoWeekYear","GG"),A("weekYear",1),A("isoWeekYear",1),lt("G",it),lt("g",it),lt("GG",Q,G),lt("gg",Q,G),lt("GGGG",tt,Z),lt("gggg",tt,Z),lt("GGGGG",et,q),lt("ggggg",et,q),ut(["gggg","ggggg","GGGG","GGGGG"],function(t,e,a,i){e[i.substr(0,2)]=C(t)}),ut(["gg","GG"],function(t,e,a,i){e[i]=m.parseTwoDigitYear(t)}),U("Q",0,"Qo","quarter"),W("quarter","Q"),A("quarter",7),lt("Q",B),ct("Q",function(t,e){e[mt]=3*(C(t)-1)}),U("D",["DD",2],"Do","date"),W("date","D"),A("date",9),lt("D",Q),lt("DD",Q,G),lt("Do",function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient}),ct(["D","DD"],pt),ct("Do",function(t,e){e[pt]=C(t.match(Q)[0])});X=bt("Date",!0);U("DDD",["DDDD",3],"DDDo","dayOfYear"),W("dayOfYear","DDD"),A("dayOfYear",4),lt("DDD",K),lt("DDDD",z),ct(["DDD","DDDD"],function(t,e,a){a._dayOfYear=C(t)}),U("m",["mm",2],0,"minute"),W("minute","m"),A("minute",14),lt("m",Q),lt("mm",Q,G),ct(["m","mm"],yt);Ot=bt("Minutes",!1);U("s",["ss",2],0,"second"),W("second","s"),A("second",15),lt("s",Q),lt("ss",Q,G),ct(["s","ss"],_t);var ia,$t=bt("Seconds",!1);for(U("S",0,0,function(){return~~(this.millisecond()/100)}),U(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),U(0,["SSS",3],0,"millisecond"),U(0,["SSSS",4],0,function(){return 10*this.millisecond()}),U(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),U(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),U(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),U(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),U(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),W("millisecond","ms"),A("millisecond",16),lt("S",K,B),lt("SS",K,G),lt("SSS",K,z),ia="SSSS";ia.length<=9;ia+="S")lt(ia,at);function sa(t,e){e[Dt]=C(1e3*("0."+t))}for(ia="S";ia.length<=9;ia+="S")ct(ia,sa);Gt=bt("Milliseconds",!1);U("z",0,0,"zoneAbbr"),U("zz",0,0,"zoneName");tt=v.prototype;function na(t){return t}tt.add=ze,tt.calendar=function(t,e){var a=t||be(),t=Ie(a,this).startOf("day"),t=m.calendarFormat(this,t)||"sameElse",e=e&&(S(e[t])?e[t].call(this,a):e[t]);return this.format(e||this.localeData().calendar(t,this,be(a)))},tt.clone=function(){return new v(this)},tt.diff=function(t,e,a){var i,s,n;if(!this.isValid())return NaN;if(!(i=Ie(t,this)).isValid())return NaN;switch(s=6e4*(i.utcOffset()-this.utcOffset()),e=E(e)){case"year":n=Ze(this,i)/12;break;case"month":n=Ze(this,i);break;case"quarter":n=Ze(this,i)/3;break;case"second":n=(this-i)/1e3;break;case"minute":n=(this-i)/6e4;break;case"hour":n=(this-i)/36e5;break;case"day":n=(this-i-s)/864e5;break;case"week":n=(this-i-s)/6048e5;break;default:n=this-i}return a?n:w(n)},tt.endOf=function(t){var e;if(void 0===(t=E(t))||"millisecond"===t||!this.isValid())return this;var a=this._isUTC?ta:Ke;switch(t){case"year":e=a(this.year()+1,0,1)-1;break;case"quarter":e=a(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":e=a(this.year(),this.month()+1,1)-1;break;case"week":e=a(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":e=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":e=a(this.year(),this.month(),this.date()+1)-1;break;case"hour":e=this._d.valueOf(),e+=36e5-Xe(e+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":e=this._d.valueOf(),e+=6e4-Xe(e,6e4)-1;break;case"second":e=this._d.valueOf(),e+=1e3-Xe(e,1e3)-1}return this._d.setTime(e),m.updateOffset(this,!0),this},tt.format=function(t){t=t||(this.isUtc()?m.defaultFormatUtc:m.defaultFormat);t=j(this,t);return this.localeData().postformat(t)},tt.from=function(t,e){return this.isValid()&&(k(t)&&t.isValid()||be(t).isValid())?Ue({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},tt.fromNow=function(t){return this.from(be(),t)},tt.to=function(t,e){return this.isValid()&&(k(t)&&t.isValid()||be(t).isValid())?Ue({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},tt.toNow=function(t){return this.to(be(),t)},tt.get=function(t){return S(this[t=E(t)])?this[t]():this},tt.invalidAt=function(){return p(this).overflow},tt.isAfter=function(t,e){t=k(t)?t:be(t);return!(!this.isValid()||!t.isValid())&&("millisecond"===(e=E(e)||"millisecond")?this.valueOf()>t.valueOf():t.valueOf()<this.clone().startOf(e).valueOf())},tt.isBefore=function(t,e){t=k(t)?t:be(t);return!(!this.isValid()||!t.isValid())&&("millisecond"===(e=E(e)||"millisecond")?this.valueOf()<t.valueOf():this.clone().endOf(e).valueOf()<t.valueOf())},tt.isBetween=function(t,e,a,i){t=k(t)?t:be(t),e=k(e)?e:be(e);return!!(this.isValid()&&t.isValid()&&e.isValid())&&("("===(i=i||"()")[0]?this.isAfter(t,a):!this.isBefore(t,a))&&(")"===i[1]?this.isBefore(e,a):!this.isAfter(e,a))},tt.isSame=function(t,e){var t=k(t)?t:be(t);return!(!this.isValid()||!t.isValid())&&("millisecond"===(e=E(e)||"millisecond")?this.valueOf()===t.valueOf():(t=t.valueOf(),this.clone().startOf(e).valueOf()<=t&&t<=this.clone().endOf(e).valueOf()))},tt.isSameOrAfter=function(t,e){return this.isSame(t,e)||this.isAfter(t,e)},tt.isSameOrBefore=function(t,e){return this.isSame(t,e)||this.isBefore(t,e)},tt.isValid=function(){return g(this)},tt.lang=J,tt.locale=qe,tt.localeData=Qe,tt.max=xe,tt.min=Se,tt.parsingFlags=function(){return c({},p(this))},tt.set=function(a,t){if("object"==typeof a)for(var e=function(){var t,e=[];for(t in a)e.push({unit:t,priority:N[t]});return e.sort(function(t,e){return t.priority-e.priority}),e}(a=L(a)),i=0;i<e.length;i++)this[e[i].unit](a[e[i].unit]);else if(S(this[a=E(a)]))return this[a](t);return this},tt.startOf=function(t){var e;if(void 0===(t=E(t))||"millisecond"===t||!this.isValid())return this;var a=this._isUTC?ta:Ke;switch(t){case"year":e=a(this.year(),0,1);break;case"quarter":e=a(this.year(),this.month()-this.month()%3,1);break;case"month":e=a(this.year(),this.month(),1);break;case"week":e=a(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":e=a(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":e=a(this.year(),this.month(),this.date());break;case"hour":e=this._d.valueOf(),e-=Xe(e+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":e=this._d.valueOf(),e-=Xe(e,6e4);break;case"second":e=this._d.valueOf(),e-=Xe(e,1e3)}return this._d.setTime(e),m.updateOffset(this,!0),this},tt.subtract=rt,tt.toArray=function(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]},tt.toObject=function(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}},tt.toDate=function(){return new Date(this.valueOf())},tt.toISOString=function(t){if(!this.isValid())return null;var e=!0!==t,t=e?this.clone().utc():this;return t.year()<0||9999<t.year()?j(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):S(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",j(t,"Z")):j(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},tt.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var t="moment",e="";this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z");var a="["+t+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY";return this.format(a+t+"-MM-DD[T]HH:mm:ss.SSS"+(e+'[")]'))},tt.toJSON=function(){return this.isValid()?this.toISOString():null},tt.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},tt.unix=function(){return Math.floor(this.valueOf()/1e3)},tt.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},tt.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},tt.year=Yt,tt.isLeapYear=function(){return Ct(this.year())},tt.weekYear=function(t){return aa.call(this,t,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},tt.isoWeekYear=function(t){return aa.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)},tt.quarter=tt.quarters=function(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)},tt.month=Lt,tt.daysInMonth=function(){return Pt(this.year(),this.month())},tt.week=tt.weeks=function(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")},tt.isoWeek=tt.isoWeeks=function(t){var e=Vt(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")},tt.weeksInYear=function(){var t=this.localeData()._week;return Ut(this.year(),t.dow,t.doy)},tt.isoWeeksInYear=function(){return Ut(this.year(),1,4)},tt.date=X,tt.day=tt.days=function(t){if(!this.isValid())return null!=t?this:NaN;var e,a,i=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=t?(e=t,a=this.localeData(),t="string"!=typeof e?e:isNaN(e)?"number"==typeof(e=a.weekdaysParse(e))?e:null:parseInt(e,10),this.add(t-i,"d")):i},tt.weekday=function(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")},tt.isoWeekday=function(t){if(!this.isValid())return null!=t?this:NaN;if(null==t)return this.day()||7;var e=(e=t,t=this.localeData(),"string"==typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e);return this.day(this.day()%7?e:e-7)},tt.dayOfYear=function(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")},tt.hour=tt.hours=ee,tt.minute=tt.minutes=Ot,tt.second=tt.seconds=$t,tt.millisecond=tt.milliseconds=Gt,tt.utcOffset=function(t,e,a){var i,s=this._offset||0;if(!this.isValid())return null!=t?this:NaN;if(null==t)return this._isUTC?s:He(this);if("string"==typeof t){if(null===(t=Ae(nt,t)))return this}else Math.abs(t)<16&&!a&&(t*=60);return!this._isUTC&&e&&(i=He(this)),this._offset=t,this._isUTC=!0,null!=i&&this.add(i,"m"),s!==t&&(!e||this._changeInProgress?Ge(this,Ue(t-s,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,m.updateOffset(this,!0),this._changeInProgress=null)),this},tt.utc=function(t){return this.utcOffset(0,t)},tt.local=function(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(He(this),"m")),this},tt.parseZone=function(){var t;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(t=Ae(st,this._i))?this.utcOffset(t):this.utcOffset(0,!0)),this},tt.hasAlignedHourOffset=function(t){return!!this.isValid()&&(t=t?be(t).utcOffset():0,(this.utcOffset()-t)%60==0)},tt.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},tt.isLocal=function(){return!!this.isValid()&&!this._isUTC},tt.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},tt.isUtc=Fe,tt.isUTC=Fe,tt.zoneAbbr=function(){return this._isUTC?"UTC":""},tt.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},tt.dates=a("dates accessor is deprecated. Use date instead.",X),tt.months=a("months accessor is deprecated. Use month instead",Lt),tt.years=a("years accessor is deprecated. Use year instead",Yt),tt.zone=a("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(t,e){return null!=t?(this.utcOffset(t="string"!=typeof t?-t:t,e),this):-this.utcOffset()}),tt.isDSTShifted=a("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){if(!n(this._isDSTShifted))return this._isDSTShifted;var t,e={};return D(e,this),(e=Me(e))._a?(t=(e._isUTC?u:be)(e._a),this._isDSTShifted=this.isValid()&&0<M(e._a,t.toArray())):this._isDSTShifted=!1,this._isDSTShifted});Z=P.prototype;function ra(t,e,a,i){var s=he(),e=u().set(i,e);return s[a](e,t)}function oa(t,e,a){if(l(t)&&(e=t,t=void 0),t=t||"",null!=e)return ra(t,e,a,"month");for(var i=[],s=0;s<12;s++)i[s]=ra(t,s,a,"month");return i}function la(t,e,a,i){"boolean"==typeof t?l(e)&&(a=e,e=void 0):(e=t,t=!1,l(a=e)&&(a=e,e=void 0)),e=e||"";var s=he(),n=t?s._week.dow:0;if(null!=a)return ra(e,(a+n)%7,i,"day");for(var r=[],o=0;o<7;o++)r[o]=ra(e,(o+n)%7,i,"day");return r}Z.calendar=function(t,e,a){t=this._calendar[t]||this._calendar.sameElse;return S(t)?t.call(e,a):t},Z.longDateFormat=function(t){var e=this._longDateFormat[t],a=this._longDateFormat[t.toUpperCase()];return e||!a?e:(this._longDateFormat[t]=a.replace(/MMMM|MM|DD|dddd/g,function(t){return t.slice(1)}),this._longDateFormat[t])},Z.invalidDate=function(){return this._invalidDate},Z.ordinal=function(t){return this._ordinal.replace("%d",t)},Z.preparse=na,Z.postformat=na,Z.relativeTime=function(t,e,a,i){var s=this._relativeTime[a];return S(s)?s(t,e,a,i):s.replace(/%d/i,t)},Z.pastFuture=function(t,e){t=this._relativeTime[0<t?"future":"past"];return S(t)?t(e):t.replace(/%s/i,e)},Z.set=function(t){var e,a;for(a in t)S(e=t[a])?this[a]=e:this["_"+a]=e;this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},Z.months=function(t,e){return t?(r(this._months)?this._months:this._months[(this._months.isFormat||Tt).test(e)?"format":"standalone"])[t.month()]:r(this._months)?this._months:this._months.standalone},Z.monthsShort=function(t,e){return t?(r(this._monthsShort)?this._monthsShort:this._monthsShort[Tt.test(e)?"format":"standalone"])[t.month()]:r(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},Z.monthsParse=function(t,e,a){var i,s;if(this._monthsParseExact)return function(t,e,a){var i,s,n,t=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],i=0;i<12;++i)n=u([2e3,i]),this._shortMonthsParse[i]=this.monthsShort(n,"").toLocaleLowerCase(),this._longMonthsParse[i]=this.months(n,"").toLocaleLowerCase();return a?"MMM"===e?-1!==(s=Mt.call(this._shortMonthsParse,t))?s:null:-1!==(s=Mt.call(this._longMonthsParse,t))?s:null:"MMM"===e?-1!==(s=Mt.call(this._shortMonthsParse,t))||-1!==(s=Mt.call(this._longMonthsParse,t))?s:null:-1!==(s=Mt.call(this._longMonthsParse,t))||-1!==(s=Mt.call(this._shortMonthsParse,t))?s:null}.call(this,t,e,a);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),i=0;i<12;i++){if(s=u([2e3,i]),a&&!this._longMonthsParse[i]&&(this._longMonthsParse[i]=new RegExp("^"+this.months(s,"").replace(".","")+"$","i"),this._shortMonthsParse[i]=new RegExp("^"+this.monthsShort(s,"").replace(".","")+"$","i")),a||this._monthsParse[i]||(s="^"+this.months(s,"")+"|^"+this.monthsShort(s,""),this._monthsParse[i]=new RegExp(s.replace(".",""),"i")),a&&"MMMM"===e&&this._longMonthsParse[i].test(t))return i;if(a&&"MMM"===e&&this._shortMonthsParse[i].test(t))return i;if(!a&&this._monthsParse[i].test(t))return i}},Z.monthsRegex=function(t){return this._monthsParseExact?(f(this,"_monthsRegex")||It.call(this),t?this._monthsStrictRegex:this._monthsRegex):(f(this,"_monthsRegex")||(this._monthsRegex=At),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)},Z.monthsShortRegex=function(t){return this._monthsParseExact?(f(this,"_monthsRegex")||It.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(f(this,"_monthsShortRegex")||(this._monthsShortRegex=Nt),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)},Z.week=function(t){return Vt(t,this._week.dow,this._week.doy).week},Z.firstDayOfYear=function(){return this._week.doy},Z.firstDayOfWeek=function(){return this._week.dow},Z.weekdays=function(t,e){e=r(this._weekdays)?this._weekdays:this._weekdays[t&&!0!==t&&this._weekdays.isFormat.test(e)?"format":"standalone"];return!0===t?jt(e,this._week.dow):t?e[t.day()]:e},Z.weekdaysMin=function(t){return!0===t?jt(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin},Z.weekdaysShort=function(t){return!0===t?jt(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort},Z.weekdaysParse=function(t,e,a){var i,s;if(this._weekdaysParseExact)return function(t,e,a){var i,s,n,t=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],i=0;i<7;++i)n=u([2e3,1]).day(i),this._minWeekdaysParse[i]=this.weekdaysMin(n,"").toLocaleLowerCase(),this._shortWeekdaysParse[i]=this.weekdaysShort(n,"").toLocaleLowerCase(),this._weekdaysParse[i]=this.weekdays(n,"").toLocaleLowerCase();return a?"dddd"===e?-1!==(s=Mt.call(this._weekdaysParse,t))?s:null:"ddd"===e?-1!==(s=Mt.call(this._shortWeekdaysParse,t))?s:null:-1!==(s=Mt.call(this._minWeekdaysParse,t))?s:null:"dddd"===e?-1!==(s=Mt.call(this._weekdaysParse,t))||-1!==(s=Mt.call(this._shortWeekdaysParse,t))||-1!==(s=Mt.call(this._minWeekdaysParse,t))?s:null:"ddd"===e?-1!==(s=Mt.call(this._shortWeekdaysParse,t))||-1!==(s=Mt.call(this._weekdaysParse,t))||-1!==(s=Mt.call(this._minWeekdaysParse,t))?s:null:-1!==(s=Mt.call(this._minWeekdaysParse,t))||-1!==(s=Mt.call(this._weekdaysParse,t))||-1!==(s=Mt.call(this._shortWeekdaysParse,t))?s:null}.call(this,t,e,a);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),i=0;i<7;i++){if(s=u([2e3,1]).day(i),a&&!this._fullWeekdaysParse[i]&&(this._fullWeekdaysParse[i]=new RegExp("^"+this.weekdays(s,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[i]=new RegExp("^"+this.weekdaysShort(s,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[i]=new RegExp("^"+this.weekdaysMin(s,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[i]||(s="^"+this.weekdays(s,"")+"|^"+this.weekdaysShort(s,"")+"|^"+this.weekdaysMin(s,""),this._weekdaysParse[i]=new RegExp(s.replace(".",""),"i")),a&&"dddd"===e&&this._fullWeekdaysParse[i].test(t))return i;if(a&&"ddd"===e&&this._shortWeekdaysParse[i].test(t))return i;if(a&&"dd"===e&&this._minWeekdaysParse[i].test(t))return i;if(!a&&this._weekdaysParse[i].test(t))return i}},Z.weekdaysRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||Qt.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(f(this,"_weekdaysRegex")||(this._weekdaysRegex=zt),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)},Z.weekdaysShortRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||Qt.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(f(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Zt),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},Z.weekdaysMinRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||Qt.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(f(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=qt),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},Z.isPM=function(t){return"p"===(t+"").toLowerCase().charAt(0)},Z.meridiem=function(t,e,a){return 11<t?a?"pm":"PM":a?"am":"AM"},oe("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10;return t+(1===C(t%100/10)?"th":1==e?"st":2==e?"nd":3==e?"rd":"th")}}),m.lang=a("moment.lang is deprecated. Use moment.locale instead.",oe),m.langData=a("moment.langData is deprecated. Use moment.localeData instead.",he);var ha=Math.abs;function da(t,e,a,i){a=Ue(e,a);return t._milliseconds+=i*a._milliseconds,t._days+=i*a._days,t._months+=i*a._months,t._bubble()}function ca(t){return t<0?Math.floor(t):Math.ceil(t)}function ua(t){return 4800*t/146097}function fa(t){return 146097*t/4800}function ma(t){return function(){return this.as(t)}}et=ma("ms"),q=ma("s"),B=ma("m"),G=ma("h"),K=ma("d"),z=ma("w"),ze=ma("M"),xe=ma("Q"),Se=ma("y");function pa(t){return function(){return this.isValid()?this._data[t]:NaN}}var rt=pa("milliseconds"),ee=pa("seconds"),Ot=pa("minutes"),$t=pa("hours"),Gt=pa("days"),X=pa("months"),Yt=pa("years"),ga=Math.round,ya={ss:44,s:45,m:45,h:22,d:26,M:11},_a=Math.abs;function Da(t){return(0<t)-(t<0)||+t}function va(){if(!this.isValid())return this.localeData().invalidDate();var t=_a(this._milliseconds)/1e3,e=_a(this._days),a=_a(this._months),i=w((h=w(t/60))/60);t%=60,h%=60;var s=w(a/12),n=a%=12,r=e,o=i,l=h,a=t?t.toFixed(3).replace(/\.?0+$/,""):"",e=this.asSeconds();if(!e)return"P0D";var i=Da(this._months)!==Da(e)?"-":"",h=Da(this._days)!==Da(e)?"-":"",t=Da(this._milliseconds)!==Da(e)?"-":"";return(e<0?"-":"")+"P"+(s?i+s+"Y":"")+(n?i+n+"M":"")+(r?h+r+"D":"")+(o||l||a?"T":"")+(o?t+o+"H":"")+(l?t+l+"M":"")+(a?t+a+"S":"")}Z=Oe.prototype;return Z.isValid=function(){return this._isValid},Z.abs=function(){var t=this._data;return this._milliseconds=ha(this._milliseconds),this._days=ha(this._days),this._months=ha(this._months),t.milliseconds=ha(t.milliseconds),t.seconds=ha(t.seconds),t.minutes=ha(t.minutes),t.hours=ha(t.hours),t.months=ha(t.months),t.years=ha(t.years),this},Z.add=function(t,e){return da(this,t,e,1)},Z.subtract=function(t,e){return da(this,t,e,-1)},Z.as=function(t){if(!this.isValid())return NaN;var e,a,i=this._milliseconds;if("month"===(t=E(t))||"quarter"===t||"year"===t)switch(e=this._days+i/864e5,a=this._months+ua(e),t){case"month":return a;case"quarter":return a/3;case"year":return a/12}else switch(e=this._days+Math.round(fa(this._months)),t){case"week":return e/7+i/6048e5;case"day":return e+i/864e5;case"hour":return 24*e+i/36e5;case"minute":return 1440*e+i/6e4;case"second":return 86400*e+i/1e3;case"millisecond":return Math.floor(864e5*e)+i;default:throw new Error("Unknown unit "+t)}},Z.asMilliseconds=et,Z.asSeconds=q,Z.asMinutes=B,Z.asHours=G,Z.asDays=K,Z.asWeeks=z,Z.asMonths=ze,Z.asQuarters=xe,Z.asYears=Se,Z.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*C(this._months/12):NaN},Z._bubble=function(){var t=this._milliseconds,e=this._days,a=this._months,i=this._data;return 0<=t&&0<=e&&0<=a||t<=0&&e<=0&&a<=0||(t+=864e5*ca(fa(a)+e),a=e=0),i.milliseconds=t%1e3,t=w(t/1e3),i.seconds=t%60,t=w(t/60),i.minutes=t%60,t=w(t/60),i.hours=t%24,a+=t=w(ua(e+=w(t/24))),e-=ca(fa(t)),t=w(a/12),a%=12,i.days=e,i.months=a,i.years=t,this},Z.clone=function(){return Ue(this)},Z.get=function(t){return t=E(t),this.isValid()?this[t+"s"]():NaN},Z.milliseconds=rt,Z.seconds=ee,Z.minutes=Ot,Z.hours=$t,Z.days=Gt,Z.weeks=function(){return w(this.days()/7)},Z.months=X,Z.years=Yt,Z.humanize=function(t){if(!this.isValid())return this.localeData().invalidDate();var e,a,i,s,n,r,o,l=this.localeData(),h=(e=!t,a=l,h=Ue(this).abs(),i=ga(h.as("s")),s=ga(h.as("m")),n=ga(h.as("h")),r=ga(h.as("d")),o=ga(h.as("M")),h=ga(h.as("y")),(h=(i<=ya.ss?["s",i]:i<ya.s&&["ss",i])||s<=1&&["m"]||s<ya.m&&["mm",s]||n<=1&&["h"]||n<ya.h&&["hh",n]||r<=1&&["d"]||r<ya.d&&["dd",r]||o<=1&&["M"]||o<ya.M&&["MM",o]||h<=1&&["y"]||["yy",h])[2]=e,h[3]=0<+this,h[4]=a,function(t,e,a,i,s){return s.relativeTime(e||1,!!a,t,i)}.apply(null,h));return t&&(h=l.pastFuture(+this,h)),l.postformat(h)},Z.toISOString=va,Z.toString=va,Z.toJSON=va,Z.locale=qe,Z.localeData=Qe,Z.toIsoString=a("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",va),Z.lang=J,U("X",0,0,"unix"),U("x",0,0,"valueOf"),lt("x",it),lt("X",/[+-]?\d+(\.\d{1,3})?/),ct("X",function(t,e,a){a._d=new Date(1e3*parseFloat(t,10))}),ct("x",function(t,e,a){a._d=new Date(C(t))}),m.version="2.24.0",t=be,m.fn=tt,m.min=function(){return Pe("isBefore",[].slice.call(arguments,0))},m.max=function(){return Pe("isAfter",[].slice.call(arguments,0))},m.now=function(){return Date.now?Date.now():+new Date},m.utc=u,m.unix=function(t){return be(1e3*t)},m.months=function(t,e){return oa(t,e,"months")},m.isDate=h,m.locale=oe,m.invalid=y,m.duration=Ue,m.isMoment=k,m.weekdays=function(t,e,a){return la(t,e,a,"weekdays")},m.parseZone=function(){return be.apply(null,arguments).parseZone()},m.localeData=he,m.isDuration=We,m.monthsShort=function(t,e){return oa(t,e,"monthsShort")},m.weekdaysMin=function(t,e,a){return la(t,e,a,"weekdaysMin")},m.defineLocale=le,m.updateLocale=function(t,e){var a,i;return null!=e?(i=ae,(e=new P(e=x(i=null!=(a=re(t))?a._config:i,e))).parentLocale=ie[t],ie[t]=e,oe(t)):null!=ie[t]&&(null!=ie[t].parentLocale?ie[t]=ie[t].parentLocale:null!=ie[t]&&delete ie[t]),ie[t]},m.locales=function(){return T(ie)},m.weekdaysShort=function(t,e,a){return la(t,e,a,"weekdaysShort")},m.normalizeUnits=E,m.relativeTimeRounding=function(t){return void 0===t?ga:"function"==typeof t&&(ga=t,!0)},m.relativeTimeThreshold=function(t,e){return void 0!==ya[t]&&(void 0===e?ya[t]:(ya[t]=e,"s"===t&&(ya.ss=e-1),!0))},m.calendarFormat=function(t,e){e=t.diff(e,"days",!0);return e<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},m.prototype=tt,m.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},m}),function(t,a){var e,i;"function"==typeof define&&define.amd?define(["moment","jquery"],function(t,e){return e.fn||(e.fn={}),"function"!=typeof t&&t.default&&(t=t.default),a(t,e)}):"object"==typeof module&&module.exports?((e="undefined"!=typeof window?window.jQuery:void 0)||(e=require("jquery")).fn||(e.fn={}),i="undefined"!=typeof window&&void 0!==window.moment?window.moment:require("moment"),module.exports=a(i,e)):t.daterangepicker=a(t.moment,t.jQuery)}(this,function(O,W){function i(t,e,a){var i,s,n,r;if(this.parentEl="body",this.element=W(t),this.startDate=O().startOf("day"),this.endDate=O().endOf("day"),this.minDate=!1,this.maxDate=!1,this.maxSpan=!1,this.autoApply=!1,this.singleDatePicker=!1,this.showDropdowns=!1,this.minYear=O().subtract(100,"year").format("YYYY"),this.maxYear=O().add(100,"year").format("YYYY"),this.showWeekNumbers=!1,this.showISOWeekNumbers=!1,this.showCustomRangeLabel=!0,this.timePicker=!1,this.timePicker24Hour=!1,this.timePickerIncrement=1,this.timePickerSeconds=!1,this.linkedCalendars=!0,this.autoUpdateInput=!0,this.alwaysShowCalendars=!1,this.ranges={},this.opens="right",this.element.hasClass("pull-right")&&(this.opens="left"),this.drops="down",this.element.hasClass("dropup")&&(this.drops="up"),this.buttonClasses="btn btn-sm",this.applyButtonClasses="btn-primary",this.cancelButtonClasses="btn-default",this.locale={direction:"ltr",format:O.localeData().longDateFormat("L"),separator:" - ",applyLabel:"Apply",cancelLabel:"Cancel",weekLabel:"W",customRangeLabel:"Custom Range",daysOfWeek:O.weekdaysMin(),monthNames:O.monthsShort(),firstDay:O.localeData().firstDayOfWeek()},this.callback=function(){},this.isShowing=!1,this.leftCalendar={},this.rightCalendar={},"object"==typeof e&&null!==e||(e={}),"string"==typeof(e=W.extend(this.element.data(),e)).template||e.template instanceof W||(e.template='<div class="daterangepicker"><div class="ranges"></div><div class="drp-calendar left"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-calendar right"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-buttons"><span class="drp-selected"></span><button class="cancelBtn" type="button"></button><button class="applyBtn" disabled="disabled" type="button"></button> </div></div>'),this.parentEl=e.parentEl&&W(e.parentEl).length?W(e.parentEl):W(this.parentEl),this.container=W(e.template).appendTo(this.parentEl),"object"==typeof e.locale&&("string"==typeof e.locale.direction&&(this.locale.direction=e.locale.direction),"string"==typeof e.locale.format&&(this.locale.format=e.locale.format),"string"==typeof e.locale.separator&&(this.locale.separator=e.locale.separator),"object"==typeof e.locale.daysOfWeek&&(this.locale.daysOfWeek=e.locale.daysOfWeek.slice()),"object"==typeof e.locale.monthNames&&(this.locale.monthNames=e.locale.monthNames.slice()),"number"==typeof e.locale.firstDay&&(this.locale.firstDay=e.locale.firstDay),"string"==typeof e.locale.applyLabel&&(this.locale.applyLabel=e.locale.applyLabel),"string"==typeof e.locale.cancelLabel&&(this.locale.cancelLabel=e.locale.cancelLabel),"string"==typeof e.locale.weekLabel&&(this.locale.weekLabel=e.locale.weekLabel),"string"==typeof e.locale.customRangeLabel&&((l=document.createElement("textarea")).innerHTML=e.locale.customRangeLabel,h=l.value,this.locale.customRangeLabel=h)),this.container.addClass(this.locale.direction),"string"==typeof e.startDate&&(this.startDate=O(e.startDate,this.locale.format)),"string"==typeof e.endDate&&(this.endDate=O(e.endDate,this.locale.format)),"string"==typeof e.minDate&&(this.minDate=O(e.minDate,this.locale.format)),"string"==typeof e.maxDate&&(this.maxDate=O(e.maxDate,this.locale.format)),"object"==typeof e.startDate&&(this.startDate=O(e.startDate)),"object"==typeof e.endDate&&(this.endDate=O(e.endDate)),"object"==typeof e.minDate&&(this.minDate=O(e.minDate)),"object"==typeof e.maxDate&&(this.maxDate=O(e.maxDate)),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),"string"==typeof e.applyButtonClasses&&(this.applyButtonClasses=e.applyButtonClasses),"string"==typeof e.applyClass&&(this.applyButtonClasses=e.applyClass),"string"==typeof e.cancelButtonClasses&&(this.cancelButtonClasses=e.cancelButtonClasses),"string"==typeof e.cancelClass&&(this.cancelButtonClasses=e.cancelClass),"object"==typeof e.maxSpan&&(this.maxSpan=e.maxSpan),"object"==typeof e.dateLimit&&(this.maxSpan=e.dateLimit),"string"==typeof e.opens&&(this.opens=e.opens),"string"==typeof e.drops&&(this.drops=e.drops),"boolean"==typeof e.showWeekNumbers&&(this.showWeekNumbers=e.showWeekNumbers),"boolean"==typeof e.showISOWeekNumbers&&(this.showISOWeekNumbers=e.showISOWeekNumbers),"string"==typeof e.buttonClasses&&(this.buttonClasses=e.buttonClasses),"object"==typeof e.buttonClasses&&(this.buttonClasses=e.buttonClasses.join(" ")),"boolean"==typeof e.showDropdowns&&(this.showDropdowns=e.showDropdowns),"number"==typeof e.minYear&&(this.minYear=e.minYear),"number"==typeof e.maxYear&&(this.maxYear=e.maxYear),"boolean"==typeof e.showCustomRangeLabel&&(this.showCustomRangeLabel=e.showCustomRangeLabel),"boolean"==typeof e.singleDatePicker&&(this.singleDatePicker=e.singleDatePicker,this.singleDatePicker&&(this.endDate=this.startDate.clone())),"boolean"==typeof e.timePicker&&(this.timePicker=e.timePicker),"boolean"==typeof e.timePickerSeconds&&(this.timePickerSeconds=e.timePickerSeconds),"number"==typeof e.timePickerIncrement&&(this.timePickerIncrement=e.timePickerIncrement),"boolean"==typeof e.timePicker24Hour&&(this.timePicker24Hour=e.timePicker24Hour),"boolean"==typeof e.autoApply&&(this.autoApply=e.autoApply),"boolean"==typeof e.autoUpdateInput&&(this.autoUpdateInput=e.autoUpdateInput),"boolean"==typeof e.linkedCalendars&&(this.linkedCalendars=e.linkedCalendars),"function"==typeof e.isInvalidDate&&(this.isInvalidDate=e.isInvalidDate),"function"==typeof e.isCustomDate&&(this.isCustomDate=e.isCustomDate),"boolean"==typeof e.alwaysShowCalendars&&(this.alwaysShowCalendars=e.alwaysShowCalendars),0!=this.locale.firstDay)for(var o=this.locale.firstDay;0<o;)this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift()),o--;if(void 0===e.startDate&&void 0===e.endDate&&W(this.element).is(":text")&&(r=i=null,2==(t=(n=W(this.element).val()).split(this.locale.separator)).length?(r=O(t[0],this.locale.format),i=O(t[1],this.locale.format)):this.singleDatePicker&&""!==n&&(r=O(n,this.locale.format),i=O(n,this.locale.format)),null!==r&&null!==i&&(this.setStartDate(r),this.setEndDate(i))),"object"==typeof e.ranges){for(s in e.ranges){r="string"==typeof e.ranges[s][0]?O(e.ranges[s][0],this.locale.format):O(e.ranges[s][0]),i="string"==typeof e.ranges[s][1]?O(e.ranges[s][1],this.locale.format):O(e.ranges[s][1]),this.minDate&&r.isBefore(this.minDate)&&(r=this.minDate.clone());var l,h,d=this.maxDate;(d=this.maxSpan&&d&&r.clone().add(this.maxSpan).isAfter(d)?r.clone().add(this.maxSpan):d)&&i.isAfter(d)&&(i=d.clone()),this.minDate&&i.isBefore(this.minDate,this.timepicker?"minute":"day")||d&&r.isAfter(d,this.timepicker?"minute":"day")||((l=document.createElement("textarea")).innerHTML=s,h=l.value,this.ranges[h]=[r,i])}var c="<ul>";for(s in this.ranges)c+='<li data-range-key="'+s+'">'+s+"</li>";this.showCustomRangeLabel&&(c+='<li data-range-key="'+this.locale.customRangeLabel+'">'+this.locale.customRangeLabel+"</li>"),c+="</ul>",this.container.find(".ranges").prepend(c)}"function"==typeof a&&(this.callback=a),this.timePicker||(this.startDate=this.startDate.startOf("day"),this.endDate=this.endDate.endOf("day"),this.container.find(".calendar-time").hide()),this.timePicker&&this.autoApply&&(this.autoApply=!1),this.autoApply&&this.container.addClass("auto-apply"),"object"==typeof e.ranges&&this.container.addClass("show-ranges"),this.singleDatePicker&&(this.container.addClass("single"),this.container.find(".drp-calendar.left").addClass("single"),this.container.find(".drp-calendar.left").show(),this.container.find(".drp-calendar.right").hide(),this.timePicker||this.container.addClass("auto-apply")),(void 0===e.ranges&&!this.singleDatePicker||this.alwaysShowCalendars)&&this.container.addClass("show-calendar"),this.container.addClass("opens"+this.opens),this.container.find(".applyBtn, .cancelBtn").addClass(this.buttonClasses),this.applyButtonClasses.length&&this.container.find(".applyBtn").addClass(this.applyButtonClasses),this.cancelButtonClasses.length&&this.container.find(".cancelBtn").addClass(this.cancelButtonClasses),this.container.find(".applyBtn").html(this.locale.applyLabel),this.container.find(".cancelBtn").html(this.locale.cancelLabel),this.container.find(".drp-calendar").on("click.daterangepicker",".prev",W.proxy(this.clickPrev,this)).on("click.daterangepicker",".next",W.proxy(this.clickNext,this)).on("mousedown.daterangepicker","td.available",W.proxy(this.clickDate,this)).on("mouseenter.daterangepicker","td.available",W.proxy(this.hoverDate,this)).on("change.daterangepicker","select.yearselect",W.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.monthselect",W.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.hourselect,select.minuteselect,select.secondselect,select.ampmselect",W.proxy(this.timeChanged,this)),this.container.find(".ranges").on("click.daterangepicker","li",W.proxy(this.clickRange,this)),this.container.find(".drp-buttons").on("click.daterangepicker","button.applyBtn",W.proxy(this.clickApply,this)).on("click.daterangepicker","button.cancelBtn",W.proxy(this.clickCancel,this)),this.element.is("input")||this.element.is("button")?this.element.on({"click.daterangepicker":W.proxy(this.show,this),"focus.daterangepicker":W.proxy(this.show,this),"keyup.daterangepicker":W.proxy(this.elementChanged,this),"keydown.daterangepicker":W.proxy(this.keydown,this)}):(this.element.on("click.daterangepicker",W.proxy(this.toggle,this)),this.element.on("keydown.daterangepicker",W.proxy(this.toggle,this))),this.updateElement()}return i.prototype={constructor:i,setStartDate:function(t){"string"==typeof t&&(this.startDate=O(t,this.locale.format)),"object"==typeof t&&(this.startDate=O(t)),this.timePicker||(this.startDate=this.startDate.startOf("day")),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.maxDate&&this.startDate.isAfter(this.maxDate)&&(this.startDate=this.maxDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.floor(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.isShowing||this.updateElement(),this.updateMonthsInView()},setEndDate:function(t){"string"==typeof t&&(this.endDate=O(t,this.locale.format)),"object"==typeof t&&(this.endDate=O(t)),this.timePicker||(this.endDate=this.endDate.endOf("day")),this.timePicker&&this.timePickerIncrement&&this.endDate.minute(Math.round(this.endDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.endDate.isBefore(this.startDate)&&(this.endDate=this.startDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),this.maxSpan&&this.startDate.clone().add(this.maxSpan).isBefore(this.endDate)&&(this.endDate=this.startDate.clone().add(this.maxSpan)),this.previousRightTime=this.endDate.clone(),this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.isShowing||this.updateElement(),this.updateMonthsInView()},isInvalidDate:function(){return!1},isCustomDate:function(){return!1},updateView:function(){this.timePicker&&(this.renderTimePicker("left"),this.renderTimePicker("right"),this.endDate?this.container.find(".right .calendar-time select").removeAttr("disabled").removeClass("disabled"):this.container.find(".right .calendar-time select").attr("disabled","disabled").addClass("disabled")),this.endDate&&this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.updateMonthsInView(),this.updateCalendars(),this.updateFormInputs()},updateMonthsInView:function(){if(this.endDate){if(!this.singleDatePicker&&this.leftCalendar.month&&this.rightCalendar.month&&(this.startDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.startDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM"))&&(this.endDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.endDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM")))return;this.leftCalendar.month=this.startDate.clone().date(2),this.linkedCalendars||this.endDate.month()==this.startDate.month()&&this.endDate.year()==this.startDate.year()?this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"):this.rightCalendar.month=this.endDate.clone().date(2)}else this.leftCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&this.rightCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&(this.leftCalendar.month=this.startDate.clone().date(2),this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"));this.maxDate&&this.linkedCalendars&&!this.singleDatePicker&&this.rightCalendar.month>this.maxDate&&(this.rightCalendar.month=this.maxDate.clone().date(2),this.leftCalendar.month=this.maxDate.clone().date(2).subtract(1,"month"))},updateCalendars:function(){var t,e,a,i;this.timePicker&&(this.endDate?(e=parseInt(this.container.find(".left .hourselect").val(),10),a=parseInt(this.container.find(".left .minuteselect").val(),10),isNaN(a)&&(a=parseInt(this.container.find(".left .minuteselect option:last").val(),10)),t=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0,this.timePicker24Hour||("PM"===(i=this.container.find(".left .ampmselect").val())&&e<12&&(e+=12),"AM"===i&&12===e&&(e=0))):(e=parseInt(this.container.find(".right .hourselect").val(),10),a=parseInt(this.container.find(".right .minuteselect").val(),10),isNaN(a)&&(a=parseInt(this.container.find(".right .minuteselect option:last").val(),10)),t=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,this.timePicker24Hour||("PM"===(i=this.container.find(".right .ampmselect").val())&&e<12&&(e+=12),"AM"===i&&12===e&&(e=0))),this.leftCalendar.month.hour(e).minute(a).second(t),this.rightCalendar.month.hour(e).minute(a).second(t)),this.renderCalendar("left"),this.renderCalendar("right"),this.container.find(".ranges li").removeClass("active"),null!=this.endDate&&this.calculateChosenLabel()},renderCalendar:function(t){var e="left"==t?this.leftCalendar:this.rightCalendar,a=e.month.month(),i=e.month.year(),s=e.month.hour(),n=e.month.minute(),r=e.month.second(),o=O([i,a]).daysInMonth(),l=O([i,a,1]),h=O([i,a,o]),d=O(l).subtract(1,"month").month(),i=O(l).subtract(1,"month").year(),a=O([i,d]).daysInMonth(),o=l.day();(e=[]).firstDay=l,e.lastDay=h;for(var c=0;c<6;c++)e[c]=[];h=a-o+this.locale.firstDay+1;a<h&&(h-=7),o==this.locale.firstDay&&(h=a-6);for(var u=O([i,d,h,12,n,r]),c=0,f=0,m=0;c<42;c++,f++,u=O(u).add(24,"hour"))0<c&&f%7==0&&(f=0,m++),e[m][f]=u.clone().hour(s).minute(n).second(r),u.hour(12),this.minDate&&e[m][f].format("YYYY-MM-DD")==this.minDate.format("YYYY-MM-DD")&&e[m][f].isBefore(this.minDate)&&"left"==t&&(e[m][f]=this.minDate.clone()),this.maxDate&&e[m][f].format("YYYY-MM-DD")==this.maxDate.format("YYYY-MM-DD")&&e[m][f].isAfter(this.maxDate)&&"right"==t&&(e[m][f]=this.maxDate.clone());"left"==t?this.leftCalendar.calendar=e:this.rightCalendar.calendar=e;var p="left"==t?this.minDate:this.startDate,g=this.maxDate,y=("left"==t?this.startDate:this.endDate,this.locale.direction,'<table class="table-condensed">');y+="<thead>",y+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(y+="<th></th>"),p&&!p.isBefore(e.firstDay)||this.linkedCalendars&&"left"!=t?y+="<th></th>":y+='<th class="prev available"><span></span></th>';var d=this.locale.monthNames[e[1][1].month()]+e[1][1].format(" YYYY");if(this.showDropdowns){for(var _=e[1][1].month(),D=e[1][1].year(),v=g&&g.year()||this.maxYear,h=p&&p.year()||this.minYear,k=D==h,w=D==v,C='<select class="monthselect">',M=0;M<12;M++)(!k||p&&M>=p.month())&&(!w||g&&M<=g.month())?C+="<option value='"+M+"'"+(M===_?" selected='selected'":"")+">"+this.locale.monthNames[M]+"</option>":C+="<option value='"+M+"'"+(M===_?" selected='selected'":"")+" disabled='disabled'>"+this.locale.monthNames[M]+"</option>";C+="</select>";for(var Y='<select class="yearselect">',b=h;b<=v;b++)Y+='<option value="'+b+'"'+(b===D?' selected="selected"':"")+">"+b+"</option>";d=C+(Y+="</select>")}y+='<th colspan="5" class="month">'+d+"</th>",g&&!g.isAfter(e.lastDay)||this.linkedCalendars&&"right"!=t&&!this.singleDatePicker?y+="<th></th>":y+='<th class="next available"><span></span></th>',y+="</tr>",y+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(y+='<th class="week">'+this.locale.weekLabel+"</th>"),W.each(this.locale.daysOfWeek,function(t,e){y+="<th>"+e+"</th>"}),y+="</tr>",y+="</thead>",y+="<tbody>",null==this.endDate&&this.maxSpan&&(d=this.startDate.clone().add(this.maxSpan).endOf("day"),g&&!d.isBefore(g)||(g=d));for(m=0;m<6;m++){y+="<tr>",this.showWeekNumbers?y+='<td class="week">'+e[m][0].week()+"</td>":this.showISOWeekNumbers&&(y+='<td class="week">'+e[m][0].isoWeek()+"</td>");for(f=0;f<7;f++){var S=[];e[m][f].isSame(new Date,"day")&&S.push("today"),5<e[m][f].isoWeekday()&&S.push("weekend"),e[m][f].month()!=e[1][1].month()&&S.push("off","ends"),this.minDate&&e[m][f].isBefore(this.minDate,"day")&&S.push("off","disabled"),g&&e[m][f].isAfter(g,"day")&&S.push("off","disabled"),this.isInvalidDate(e[m][f])&&S.push("off","disabled"),e[m][f].format("YYYY-MM-DD")==this.startDate.format("YYYY-MM-DD")&&S.push("active","start-date"),null!=this.endDate&&e[m][f].format("YYYY-MM-DD")==this.endDate.format("YYYY-MM-DD")&&S.push("active","end-date"),null!=this.endDate&&e[m][f]>this.startDate&&e[m][f]<this.endDate&&S.push("in-range");var x=this.isCustomDate(e[m][f]);!1!==x&&("string"==typeof x?S.push(x):Array.prototype.push.apply(S,x));for(var P="",T=!1,c=0;c<S.length;c++)P+=S[c]+" ","disabled"==S[c]&&(T=!0);T||(P+="available"),y+='<td class="'+P.replace(/^\s+|\s+$/g,"")+'" data-title="r'+m+"c"+f+'">'+e[m][f].date()+"</td>"}y+="</tr>"}y+="</tbody>",y+="</table>",this.container.find(".drp-calendar."+t+" .calendar-table").html(y)},renderTimePicker:function(t){if("right"!=t||this.endDate){var e,a,i=this.maxDate;!this.maxSpan||this.maxDate&&!this.startDate.clone().add(this.maxSpan).isBefore(this.maxDate)||(i=this.startDate.clone().add(this.maxSpan)),"left"==t?(e=this.startDate.clone(),a=this.minDate):"right"==t&&(e=this.endDate.clone(),a=this.startDate,""!=(n=this.container.find(".drp-calendar.right .calendar-time")).html()&&(e.hour(isNaN(e.hour())?n.find(".hourselect option:selected").val():e.hour()),e.minute(isNaN(e.minute())?n.find(".minuteselect option:selected").val():e.minute()),e.second(isNaN(e.second())?n.find(".secondselect option:selected").val():e.second()),this.timePicker24Hour||("PM"===(c=n.find(".ampmselect option:selected").val())&&e.hour()<12&&e.hour(e.hour()+12),"AM"===c&&12===e.hour()&&e.hour(0))),e.isBefore(this.startDate)&&(e=this.startDate.clone()),i&&e.isAfter(i)&&(e=i.clone()));for(var s='<select class="hourselect">',n=this.timePicker24Hour?0:1,r=this.timePicker24Hour?23:12,o=n;o<=r;o++){var l=o;this.timePicker24Hour||(l=12<=e.hour()?12==o?12:o+12:12==o?0:o);var h=e.clone().hour(l),d=!1;a&&h.minute(59).isBefore(a)&&(d=!0),i&&h.minute(0).isAfter(i)&&(d=!0),l!=e.hour()||d?s+=d?'<option value="'+o+'" disabled="disabled" class="disabled">'+o+"</option>":'<option value="'+o+'">'+o+"</option>":s+='<option value="'+o+'" selected="selected">'+o+"</option>"}s+="</select> ",s+=': <select class="minuteselect">';for(var c,o=0;o<60;o+=this.timePickerIncrement){var u=o<10?"0"+o:o,h=e.clone().minute(o),d=!1;a&&h.second(59).isBefore(a)&&(d=!0),i&&h.second(0).isAfter(i)&&(d=!0),e.minute()!=o||d?s+=d?'<option value="'+o+'" disabled="disabled" class="disabled">'+u+"</option>":'<option value="'+o+'">'+u+"</option>":s+='<option value="'+o+'" selected="selected">'+u+"</option>"}if(s+="</select> ",this.timePickerSeconds){s+=': <select class="secondselect">';for(o=0;o<60;o++){u=o<10?"0"+o:o,h=e.clone().second(o),d=!1;a&&h.isBefore(a)&&(d=!0),i&&h.isAfter(i)&&(d=!0),e.second()!=o||d?s+=d?'<option value="'+o+'" disabled="disabled" class="disabled">'+u+"</option>":'<option value="'+o+'">'+u+"</option>":s+='<option value="'+o+'" selected="selected">'+u+"</option>"}s+="</select> "}this.timePicker24Hour||(s+='<select class="ampmselect">',n=c="",a&&e.clone().hour(12).minute(0).second(0).isBefore(a)&&(c=' disabled="disabled" class="disabled"'),i&&e.clone().hour(0).minute(0).second(0).isAfter(i)&&(n=' disabled="disabled" class="disabled"'),12<=e.hour()?s+='<option value="AM"'+c+'>AM</option><option value="PM" selected="selected"'+n+">PM</option>":s+='<option value="AM" selected="selected"'+c+'>AM</option><option value="PM"'+n+">PM</option>",s+="</select>"),this.container.find(".drp-calendar."+t+" .calendar-time").html(s)}},updateFormInputs:function(){this.singleDatePicker||this.endDate&&(this.startDate.isBefore(this.endDate)||this.startDate.isSame(this.endDate))?this.container.find("button.applyBtn").removeAttr("disabled"):this.container.find("button.applyBtn").attr("disabled","disabled")},move:function(){var t,e={top:0,left:0},a=W(window).width();this.parentEl.is("body")||(e={top:this.parentEl.offset().top-this.parentEl.scrollTop(),left:this.parentEl.offset().left-this.parentEl.scrollLeft()},a=this.parentEl[0].clientWidth+this.parentEl.offset().left),t="up"==this.drops?this.element.offset().top-this.container.outerHeight()-e.top:this.element.offset().top+this.element.outerHeight()-e.top,this.container.css({top:0,left:0,right:"auto"});var i,s=this.container.outerWidth();this.container["up"==this.drops?"addClass":"removeClass"]("drop-up"),"left"==this.opens?s+(a=a-this.element.offset().left-this.element.outerWidth())>W(window).width()?this.container.css({top:t,right:"auto",left:9}):this.container.css({top:t,right:a,left:"auto"}):"center"==this.opens?(i=this.element.offset().left-e.left+this.element.outerWidth()/2-s/2)<0?this.container.css({top:t,right:"auto",left:9}):i+s>W(window).width()?this.container.css({top:t,left:"auto",right:0}):this.container.css({top:t,left:i,right:"auto"}):(i=this.element.offset().left-e.left)+s>W(window).width()?this.container.css({top:t,left:"auto",right:0}):this.container.css({top:t,left:i,right:"auto"})},show:function(t){this.isShowing||(this._outsideClickProxy=W.proxy(function(t){this.outsideClick(t)},this),W(document).on("mousedown.daterangepicker",this._outsideClickProxy).on("touchend.daterangepicker",this._outsideClickProxy).on("click.daterangepicker","[data-toggle=dropdown]",this._outsideClickProxy).on("focusin.daterangepicker",this._outsideClickProxy),W(window).on("resize.daterangepicker",W.proxy(function(t){this.move(t)},this)),this.oldStartDate=this.startDate.clone(),this.oldEndDate=this.endDate.clone(),this.previousRightTime=this.endDate.clone(),this.updateView(),this.container.show(),this.move(),this.element.trigger("show.daterangepicker",this),this.isShowing=!0)},hide:function(t){this.isShowing&&(this.endDate||(this.startDate=this.oldStartDate.clone(),this.endDate=this.oldEndDate.clone()),this.startDate.isSame(this.oldStartDate)&&this.endDate.isSame(this.oldEndDate)||this.callback(this.startDate.clone(),this.endDate.clone(),this.chosenLabel),this.updateElement(),W(document).off(".daterangepicker"),W(window).off(".daterangepicker"),this.container.hide(),this.element.trigger("hide.daterangepicker",this),this.isShowing=!1)},toggle:function(t){this.isShowing?this.hide():this.show()},outsideClick:function(t){var e=W(t.target);"focusin"==t.type||e.closest(this.element).length||e.closest(this.container).length||e.closest(".calendar-table").length||(this.hide(),this.element.trigger("outsideClick.daterangepicker",this))},showCalendars:function(){this.container.addClass("show-calendar"),this.move(),this.element.trigger("showCalendar.daterangepicker",this)},hideCalendars:function(){this.container.removeClass("show-calendar"),this.element.trigger("hideCalendar.daterangepicker",this)},clickRange:function(t){var t=t.target.getAttribute("data-range-key");(this.chosenLabel=t)==this.locale.customRangeLabel?this.showCalendars():(t=this.ranges[t],this.startDate=t[0],this.endDate=t[1],this.timePicker||(this.startDate.startOf("day"),this.endDate.endOf("day")),this.alwaysShowCalendars||this.hideCalendars(),this.clickApply())},clickPrev:function(t){W(t.target).parents(".drp-calendar").hasClass("left")?(this.leftCalendar.month.subtract(1,"month"),this.linkedCalendars&&this.rightCalendar.month.subtract(1,"month")):this.rightCalendar.month.subtract(1,"month"),this.updateCalendars()},clickNext:function(t){W(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar.month.add(1,"month"):(this.rightCalendar.month.add(1,"month"),this.linkedCalendars&&this.leftCalendar.month.add(1,"month")),this.updateCalendars()},hoverDate:function(t){var e,a,s,n,r,o;W(t.target).hasClass("available")&&(e=(a=W(t.target).attr("data-title")).substr(1,1),a=a.substr(3,1),s=(W(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar:this.rightCalendar).calendar[e][a],n=this.leftCalendar,r=this.rightCalendar,o=this.startDate,this.endDate||this.container.find(".drp-calendar tbody td").each(function(t,e){var a,i;W(e).hasClass("week")||(a=(i=W(e).attr("data-title")).substr(1,1),i=i.substr(3,1),(i=(W(e).parents(".drp-calendar").hasClass("left")?n:r).calendar[a][i]).isAfter(o)&&i.isBefore(s)||i.isSame(s,"day")?W(e).addClass("in-range"):W(e).removeClass("in-range"))}))},clickDate:function(t){var e,a,i,s,n,r;W(t.target).hasClass("available")&&(e=(a=W(t.target).attr("data-title")).substr(1,1),a=a.substr(3,1),a=(W(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar:this.rightCalendar).calendar[e][a],this.endDate||a.isBefore(this.startDate,"day")?(this.timePicker&&(i=parseInt(this.container.find(".left .hourselect").val(),10),this.timePicker24Hour||("PM"===(s=this.container.find(".left .ampmselect").val())&&i<12&&(i+=12),"AM"===s&&12===i&&(i=0)),n=parseInt(this.container.find(".left .minuteselect").val(),10),isNaN(n)&&(n=parseInt(this.container.find(".left .minuteselect option:last").val(),10)),r=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0,a=a.clone().hour(i).minute(n).second(r)),this.endDate=null,this.setStartDate(a.clone())):!this.endDate&&a.isBefore(this.startDate)?this.setEndDate(this.startDate.clone()):(this.timePicker&&(i=parseInt(this.container.find(".right .hourselect").val(),10),this.timePicker24Hour||("PM"===(s=this.container.find(".right .ampmselect").val())&&i<12&&(i+=12),"AM"===s&&12===i&&(i=0)),n=parseInt(this.container.find(".right .minuteselect").val(),10),isNaN(n)&&(n=parseInt(this.container.find(".right .minuteselect option:last").val(),10)),r=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,a=a.clone().hour(i).minute(n).second(r)),this.setEndDate(a.clone()),this.autoApply&&(this.calculateChosenLabel(),this.clickApply())),this.singleDatePicker&&(this.setEndDate(this.startDate),this.timePicker||this.clickApply()),this.updateView(),t.stopPropagation())},calculateChosenLabel:function(){var t,e=!0,a=0;for(t in this.ranges){if(this.timePicker){var i=this.timePickerSeconds?"YYYY-MM-DD HH:mm:ss":"YYYY-MM-DD HH:mm";if(this.startDate.format(i)==this.ranges[t][0].format(i)&&this.endDate.format(i)==this.ranges[t][1].format(i)){e=!1,this.chosenLabel=this.container.find(".ranges li:eq("+a+")").addClass("active").attr("data-range-key");break}}else if(this.startDate.format("YYYY-MM-DD")==this.ranges[t][0].format("YYYY-MM-DD")&&this.endDate.format("YYYY-MM-DD")==this.ranges[t][1].format("YYYY-MM-DD")){e=!1,this.chosenLabel=this.container.find(".ranges li:eq("+a+")").addClass("active").attr("data-range-key");break}a++}e&&(this.showCustomRangeLabel?this.chosenLabel=this.container.find(".ranges li:last").addClass("active").attr("data-range-key"):this.chosenLabel=null,this.showCalendars())},clickApply:function(t){this.hide(),this.element.trigger("apply.daterangepicker",this)},clickCancel:function(t){this.startDate=this.oldStartDate,this.endDate=this.oldEndDate,this.hide(),this.element.trigger("cancel.daterangepicker",this)},monthOrYearChanged:function(t){var e=W(t.target).closest(".drp-calendar").hasClass("left"),a=this.container.find(".drp-calendar."+(e?"left":"right")),t=parseInt(a.find(".monthselect").val(),10),a=a.find(".yearselect").val();e||(a<this.startDate.year()||a==this.startDate.year()&&t<this.startDate.month())&&(t=this.startDate.month(),a=this.startDate.year()),this.minDate&&(a<this.minDate.year()||a==this.minDate.year()&&t<this.minDate.month())&&(t=this.minDate.month(),a=this.minDate.year()),this.maxDate&&(a>this.maxDate.year()||a==this.maxDate.year()&&t>this.maxDate.month())&&(t=this.maxDate.month(),a=this.maxDate.year()),e?(this.leftCalendar.month.month(t).year(a),this.linkedCalendars&&(this.rightCalendar.month=this.leftCalendar.month.clone().add(1,"month"))):(this.rightCalendar.month.month(t).year(a),this.linkedCalendars&&(this.leftCalendar.month=this.rightCalendar.month.clone().subtract(1,"month"))),this.updateCalendars()},timeChanged:function(t){var e=W(t.target).closest(".drp-calendar"),a=e.hasClass("left"),i=parseInt(e.find(".hourselect").val(),10),s=parseInt(e.find(".minuteselect").val(),10);isNaN(s)&&(s=parseInt(e.find(".minuteselect option:last").val(),10));var n,t=this.timePickerSeconds?parseInt(e.find(".secondselect").val(),10):0;this.timePicker24Hour||("PM"===(e=e.find(".ampmselect").val())&&i<12&&(i+=12),"AM"===e&&12===i&&(i=0)),a?((n=this.startDate.clone()).hour(i),n.minute(s),n.second(t),this.setStartDate(n),this.singleDatePicker?this.endDate=this.startDate.clone():this.endDate&&this.endDate.format("YYYY-MM-DD")==n.format("YYYY-MM-DD")&&this.endDate.isBefore(n)&&this.setEndDate(n.clone())):this.endDate&&((n=this.endDate.clone()).hour(i),n.minute(s),n.second(t),this.setEndDate(n)),this.updateCalendars(),this.updateFormInputs(),this.renderTimePicker("left"),this.renderTimePicker("right")},elementChanged:function(){var t,e,a;this.element.is("input")&&this.element.val().length&&(a=e=null,2===(t=this.element.val().split(this.locale.separator)).length&&(e=O(t[0],this.locale.format),a=O(t[1],this.locale.format)),!this.singleDatePicker&&null!==e&&null!==a||(a=e=O(this.element.val(),this.locale.format)),e.isValid()&&a.isValid()&&(this.setStartDate(e),this.setEndDate(a),this.updateView()))},keydown:function(t){9!==t.keyCode&&13!==t.keyCode||this.hide(),27===t.keyCode&&(t.preventDefault(),t.stopPropagation(),this.hide())},updateElement:function(){var t;this.element.is("input")&&this.autoUpdateInput&&(t=this.startDate.format(this.locale.format),this.singleDatePicker||(t+=this.locale.separator+this.endDate.format(this.locale.format)),t!==this.element.val()&&this.element.val(t).trigger("change"))},remove:function(){this.container.remove(),this.element.off(".daterangepicker"),this.element.removeData()}},W.fn.daterangepicker=function(t,e){var a=W.extend(!0,{},W.fn.daterangepicker.defaultOptions,t);return this.each(function(){var t=W(this);t.data("daterangepicker")&&t.data("daterangepicker").remove(),t.data("daterangepicker",new i(t,a,e))}),this},i}),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(window.jQuery)}(function(d){"use strict";var c,u;d.fn.ratingLocales={},d.fn.ratingThemes={},c={NAMESPACE:".rating",DEFAULT_MIN:0,DEFAULT_MAX:5,DEFAULT_STEP:.5,isEmpty:function(t,e){return null==t||0===t.length||e&&""===d.trim(t)},getCss:function(t,e){return t?" "+e:""},addCss:function(t,e){t.removeClass(e).addClass(e)},getDecimalPlaces:function(t){t=(""+t).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0},applyPrecision:function(t,e){return parseFloat(t.toFixed(e))},handler:function(t,e,a,i,s){e=s?e:e.split(" ").join(c.NAMESPACE+" ")+c.NAMESPACE;i||t.off(e),t.on(e,a)}},(u=function(t,e){this.$element=d(t),this._init(e)}).prototype={constructor:u,_parseAttr:function(t,e){var a,i,s=this.$element,n=s.attr("type");if("range"===n||"number"===n){switch(i=e[t]||s.data(t)||s.attr(t),t){case"min":a=c.DEFAULT_MIN;break;case"max":a=c.DEFAULT_MAX;break;default:a=c.DEFAULT_STEP}i=c.isEmpty(i)?a:i,i=parseFloat(i)}else i=parseFloat(e[t]);return isNaN(i)?a:i},_parseValue:function(t){t=parseFloat(t);return isNaN(t)&&(t=this.clearValue),!this.zeroAsNull||0!==t&&"0"!==t?t:null},_setDefault:function(t,e){c.isEmpty(this[t])&&(this[t]=e)},_initSlider:function(t){var e=this,a=e.$element.val();e.initialValue=c.isEmpty(a)?0:a,e._setDefault("min",e._parseAttr("min",t)),e._setDefault("max",e._parseAttr("max",t)),e._setDefault("step",e._parseAttr("step",t)),(isNaN(e.min)||c.isEmpty(e.min))&&(e.min=c.DEFAULT_MIN),(isNaN(e.max)||c.isEmpty(e.max))&&(e.max=c.DEFAULT_MAX),(isNaN(e.step)||c.isEmpty(e.step)||0===e.step)&&(e.step=c.DEFAULT_STEP),e.diff=e.max-e.min},_initHighlight:function(t){var e,a=this,i=a._getCaption();t=t||a.$element.val(),e=a.getWidthFromValue(t)+"%",a.$filledStars.width(e),a.cache={caption:i,width:e,val:t}},_getContainerCss:function(){var t=this;return"rating-container"+c.getCss(t.theme,"theme-"+t.theme)+c.getCss(t.rtl,"rating-rtl")+c.getCss(t.size,"rating-"+t.size)+c.getCss(t.animate,"rating-animate")+c.getCss(t.disabled||t.readonly,"rating-disabled")+c.getCss(t.containerClass,t.containerClass)+(t.displayOnly?" is-display-only":"")},_checkDisabled:function(){var t=this,e=t.$element,a=t.options;t.disabled=void 0===a.disabled?e.attr("disabled")||!1:a.disabled,t.readonly=void 0===a.readonly?e.attr("readonly")||!1:a.readonly,t.inactive=t.disabled||t.readonly,e.attr({disabled:t.disabled,readonly:t.readonly})},_addContent:function(t,e){var a=this.$container,t="clear"===t;return this.rtl?t?a.append(e):a.prepend(e):t?a.prepend(e):a.append(e)},_generateRating:function(){var t,e=this,a=e.$element,i=e.$container=d(document.createElement("div")).insertBefore(a);c.addCss(i,e._getContainerCss()),e.$rating=t=d(document.createElement("div")).attr("class","rating-stars").appendTo(i).append(e._getStars("empty")).append(e._getStars("filled")),e.$emptyStars=t.find(".empty-stars"),e.$filledStars=t.find(".filled-stars"),e._renderCaption(),e._renderClear(),e._initHighlight(),e._initCaptionTitle(),i.append(a),e.rtl&&(i=Math.max(e.$emptyStars.outerWidth(),e.$filledStars.outerWidth()),e.$emptyStars.width(i)),a.appendTo(t)},_getCaption:function(){return this.$caption&&this.$caption.length?this.$caption.html():this.defaultCaption},_setCaption:function(t){this.$caption&&this.$caption.length&&this.$caption.html(t)},_renderCaption:function(){var t=this,e=t.$element.val(),a=t.captionElement?d(t.captionElement):"";if(t.showCaption){if(e=t.fetchCaption(e),a&&a.length)return c.addCss(a,"caption"),a.html(e),void(t.$caption=a);t._addContent("caption",'<div class="caption">'+e+"</div>"),t.$caption=t.$container.find(".caption")}},_renderClear:function(){var t,e=this,a=e.clearElement?d(e.clearElement):"";if(e.showClear){if(t=e._getClearClass(),a.length)return c.addCss(a,t),a.attr({title:e.clearButtonTitle}).html(e.clearButton),void(e.$clear=a);e._addContent("clear",'<div class="'+t+'" title="'+e.clearButtonTitle+'">'+e.clearButton+"</div>"),e.$clear=e.$container.find("."+e.clearButtonBaseClass)}},_getClearClass:function(){return this.clearButtonBaseClass+" "+(this.inactive?"":this.clearButtonActiveClass)},_toggleHover:function(t){var e,a,i=this;t&&(i.hoverChangeStars&&(e=i.getWidthFromValue(i.clearValue),e=t.val<=i.clearValue?e+"%":t.width,i.$filledStars.css("width",e)),i.hoverChangeCaption&&(a=t.val<=i.clearValue?i.fetchCaption(i.clearValue):t.caption)&&i._setCaption(a+""))},_init:function(t){var a=this,e=a.$element.addClass("rating-input");return a.options=t,d.each(t,function(t,e){a[t]=e}),!a.rtl&&"rtl"!==e.attr("dir")||(a.rtl=!0,e.attr("dir","rtl")),a.starClicked=!1,a.clearClicked=!1,a._initSlider(t),a._checkDisabled(),a.displayOnly&&(a.inactive=!0,a.showClear=!1,a.hoverEnabled=!1,a.hoverChangeCaption=!1,a.hoverChangeStars=!1),a._generateRating(),a._initEvents(),a._listen(),t=a._parseValue(e.val()),e.val(t),e.removeClass("rating-loading")},_initCaptionTitle:function(){var t;this.showCaptionAsTitle&&(t=this.fetchCaption(this.$element.val()),this.$rating.attr("title",d(t).text()))},_trigChange:function(t){this._initCaptionTitle(),this.$element.trigger("change").trigger("rating:change",t)},_initEvents:function(){var s=this;s.events={_getTouchPosition:function(t){return(c.isEmpty(t.pageX)?t.originalEvent.touches[0]:t).pageX-s.$rating.offset().left},_listenClick:function(t,e){if(t.stopPropagation(),t.preventDefault(),!0===t.handled)return!1;e(t),t.handled=!0},_noMouseAction:function(t){return!s.hoverEnabled||s.inactive||t&&t.isDefaultPrevented()},initTouch:function(t){var e,a,i=s.clearValue||0;("ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch)&&!s.inactive&&(e=t.originalEvent,e=c.isEmpty(e.touches)?e.changedTouches:e.touches,e=s.events._getTouchPosition(e[0]),"touchend"===t.type?(s._setStars(e),a=[s.$element.val(),s._getCaption()],s._trigChange(a),s.starClicked=!0):(a=(t=s.calculate(e)).val<=i?s.fetchCaption(i):t.caption,e=s.getWidthFromValue(i),t=t.val<=i?e+"%":t.width,s._setCaption(a),s.$filledStars.css("width",t)))},starClick:function(t){var e;s.events._listenClick(t,function(t){return!s.inactive&&(e=s.events._getTouchPosition(t),s._setStars(e),e=[s.$element.val(),s._getCaption()],s._trigChange(e),void(s.starClicked=!0))})},clearClick:function(t){s.events._listenClick(t,function(){s.inactive||(s.clear(),s.clearClicked=!0)})},starMouseMove:function(t){s.events._noMouseAction(t)||(s.starClicked=!1,t=s.events._getTouchPosition(t),t=s.calculate(t),s._toggleHover(t),s.$element.trigger("rating:hover",[t.val,t.caption,"stars"]))},starMouseLeave:function(t){s.events._noMouseAction(t)||s.starClicked||(t=s.cache,s._toggleHover(t),s.$element.trigger("rating:hoverleave",["stars"]))},clearMouseMove:function(t){var e,a;!s.events._noMouseAction(t)&&s.hoverOnClear&&(s.clearClicked=!1,e='<span class="'+s.clearCaptionClass+'">'+s.clearCaption+"</span>",a=s.clearValue,t=s.getWidthFromValue(a)||0,s._toggleHover({caption:e,width:t,val:a}),s.$element.trigger("rating:hover",[a,e,"clear"]))},clearMouseLeave:function(t){s.events._noMouseAction(t)||s.clearClicked||!s.hoverOnClear||(t=s.cache,s._toggleHover(t),s.$element.trigger("rating:hoverleave",["clear"]))},resetForm:function(t){t&&t.isDefaultPrevented()||s.inactive||s.reset()}}},_listen:function(){var t=this,e=t.$element,a=e.closest("form"),i=t.$rating,s=t.$clear,n=t.events;return c.handler(i,"touchstart touchmove touchend",d.proxy(n.initTouch,t)),c.handler(i,"click touchstart",d.proxy(n.starClick,t)),c.handler(i,"mousemove",d.proxy(n.starMouseMove,t)),c.handler(i,"mouseleave",d.proxy(n.starMouseLeave,t)),t.showClear&&s.length&&(c.handler(s,"click touchstart",d.proxy(n.clearClick,t)),c.handler(s,"mousemove",d.proxy(n.clearMouseMove,t)),c.handler(s,"mouseleave",d.proxy(n.clearMouseLeave,t))),a.length&&c.handler(a,"reset",d.proxy(n.resetForm,t),!0),e},_getStars:function(t){for(var e='<span class="'+t+'-stars">',a=1;a<=this.stars;a++)e+='<span class="star">'+this[t+"Star"]+"</span>";return e+"</span>"},_setStars:function(t){var e=this,a=arguments.length?e.calculate(t):e.calculate(),i=e.$element,t=e._parseValue(a.val);return i.val(t),e.$filledStars.css("width",a.width),e._setCaption(a.caption),e.cache=a,i},showStars:function(t){t=this._parseValue(t);return this.$element.val(t),this._initCaptionTitle(),this._setStars()},calculate:function(t){var e=this,a=c.isEmpty(e.$element.val())?0:e.$element.val(),t=arguments.length?e.getValueFromPosition(t):a,a=e.fetchCaption(t),e=e.getWidthFromValue(t);return{caption:a,width:e+="%",val:t}},getValueFromPosition:function(t){var e=this,a=c.getDecimalPlaces(e.step),i=e.$rating.width(),i=e.diff*t/(i*e.step);return i=e.rtl?Math.floor(i):Math.ceil(i),a=c.applyPrecision(parseFloat(e.min+i*e.step),a),a=Math.max(Math.min(a,e.max),e.min),e.rtl?e.max-a:a},getWidthFromValue:function(t){var e,a=this.min,i=this.max,s=this.$emptyStars;return!t||t<=a||a===i?0:(e=(e=s.outerWidth())?s.width()/e:1,i<=t?100:(t-a)*e*100/(i-a))},fetchCaption:function(t){var e=this,a=parseFloat(t)||e.clearValue,i=e.starCaptions,s=e.starCaptionClasses,t=e.getWidthFromValue(a);return a&&a!==e.clearValue&&(a=c.applyPrecision(a,c.getDecimalPlaces(e.step))),s="function"==typeof s?s(a,t):s[a],i="function"==typeof i?i(a,t):i[a],i=c.isEmpty(i)?e.defaultCaption.replace(/\{rating}/g,a):i,'<span class="'+(c.isEmpty(s)?e.clearCaptionClass:s)+'">'+(a===e.clearValue?e.clearCaption:i)+"</span>"},destroy:function(){var t=this.$element;return c.isEmpty(this.$container)||this.$container.before(t).remove(),d.removeData(t.get(0)),t.off("rating").removeClass("rating rating-input")},create:function(t){t=t||this.options||{};return this.destroy().rating(t)},clear:function(){var t=this,e='<span class="'+t.clearCaptionClass+'">'+t.clearCaption+"</span>";return t.inactive||t._setCaption(e),t.showStars(t.clearValue).trigger("change").trigger("rating:clear")},reset:function(){return this.showStars(this.initialValue).trigger("rating:reset")},update:function(t){return arguments.length?this.showStars(t):this.$element},refresh:function(t){var e=this.$element;return t?this.destroy().rating(d.extend(!0,this.options,t)).trigger("rating:refresh"):e}},d.fn.rating=function(o){var l=Array.apply(null,arguments),h=[];switch(l.shift(),this.each(function(){var t=d(this),e=t.data("rating"),a="object"==typeof o&&o,i=a.theme||t.data("theme"),s=a.language||t.data("language")||"en",n={},r={};e||(i&&(n=d.fn.ratingThemes[i]||{}),"en"===s||c.isEmpty(d.fn.ratingLocales[s])||(r=d.fn.ratingLocales[s]),a=d.extend(!0,{},d.fn.rating.defaults,n,d.fn.ratingLocales.en,r,a,t.data()),e=new u(this,a),t.data("rating",e)),"string"==typeof o&&h.push(e[o].apply(e,l))}),h.length){case 0:return this;case 1:return void 0===h[0]?this:h[0];default:return h}},d.fn.rating.defaults={theme:"",language:"en",stars:5,filledStar:'<i class="glyphicon glyphicon-star"></i>',emptyStar:'<i class="glyphicon glyphicon-star-empty"></i>',containerClass:"",size:"md",animate:!0,displayOnly:!1,rtl:!1,showClear:!0,showCaption:!0,starCaptionClasses:{.5:"label label-danger badge-danger",1:"label label-danger badge-danger",1.5:"label label-warning badge-warning",2:"label label-warning badge-warning",2.5:"label label-info badge-info",3:"label label-info badge-info",3.5:"label label-primary badge-primary",4:"label label-primary badge-primary",4.5:"label label-success badge-success",5:"label label-success badge-success"},clearButton:'<i class="glyphicon glyphicon-minus-sign"></i>',clearButtonBaseClass:"clear-rating",clearButtonActiveClass:"clear-rating-active",clearCaptionClass:"label label-default badge-secondary",clearValue:null,captionElement:null,clearElement:null,showCaptionAsTitle:!0,hoverEnabled:!0,hoverChangeCaption:!0,hoverChangeStars:!0,hoverOnClear:!0,zeroAsNull:!0},d.fn.ratingLocales.en={defaultCaption:"{rating} Stars",starCaptions:{.5:"Half Star",1:"One Star",1.5:"One & Half Star",2:"Two Stars",2.5:"Two & Half Stars",3:"Three Stars",3.5:"Three & Half Stars",4:"Four Stars",4.5:"Four & Half Stars",5:"Five Stars"},clearButtonTitle:"Clear",clearCaption:"Not Rated"},d.fn.rating.Constructor=u,d(document).ready(function(){var t=d("input.rating");t.length&&t.removeClass("rating-loading").addClass("rating-loading").rating()})}),function(){"use strict";window.jQuery.fn.ratingThemes["krajee-fa"]={filledStar:'<i class="fa fa-star"></i>',emptyStar:'<i class="fa fa-star-o"></i>',clearButton:'<i class="fa fa-lg fa-minus-circle"></i>'}}();