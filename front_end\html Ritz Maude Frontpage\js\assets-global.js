function getFocusableElements(container){return Array.from(container.querySelectorAll("summary, a[href], button:enabled, [tabindex]:not([tabindex^='-']), [draggable], area, input:not([type=hidden]):enabled, select:enabled, textarea:enabled, object, iframe"))}document.querySelectorAll('[id^="Details-"] summary').forEach(summary=>{summary.setAttribute("role","button"),summary.setAttribute("aria-expanded",summary.parentNode.hasAttribute("open")),summary.nextElementSibling.getAttribute("id")&&summary.setAttribute("aria-controls",summary.nextElementSibling.id),summary.addEventListener("click",event=>{event.currentTarget.setAttribute("aria-expanded",!event.currentTarget.closest("details").hasAttribute("open"))}),!summary.closest("header-drawer, menu-drawer")&&summary.parentElement.addEventListener("keyup",onKeyUpEscape)});const trapFocusHandlers={};function trapFocus(container,elementToFocus=container){var elements=getFocusableElements(container),first=elements[0],last=elements[elements.length-1];removeTrapFocus(),trapFocusHandlers.focusin=event=>{event.target!==container&&event.target!==last&&event.target!==first||document.addEventListener("keydown",trapFocusHandlers.keydown)},trapFocusHandlers.focusout=function(){document.removeEventListener("keydown",trapFocusHandlers.keydown)},trapFocusHandlers.keydown=function(event){event.code.toUpperCase()==="TAB"&&(event.target===last&&!event.shiftKey&&(event.preventDefault(),first.focus()),(event.target===container||event.target===first)&&event.shiftKey&&(event.preventDefault(),last.focus()))},document.addEventListener("focusout",trapFocusHandlers.focusout),document.addEventListener("focusin",trapFocusHandlers.focusin),elementToFocus.focus(),elementToFocus.tagName==="INPUT"&&["search","text","email","url"].includes(elementToFocus.type)&&elementToFocus.value&&elementToFocus.setSelectionRange(0,elementToFocus.value.length)}try{document.querySelector(":focus-visible")}catch{focusVisiblePolyfill()}function focusVisiblePolyfill(){const navKeys=["ARROWUP","ARROWDOWN","ARROWLEFT","ARROWRIGHT","TAB","ENTER","SPACE","ESCAPE","HOME","END","PAGEUP","PAGEDOWN"];let currentFocusedElement=null,mouseClick=null;window.addEventListener("keydown",event=>{navKeys.includes(event.code.toUpperCase())&&(mouseClick=!1)}),window.addEventListener("mousedown",event=>{mouseClick=!0}),window.addEventListener("focus",()=>{currentFocusedElement&&currentFocusedElement.classList.remove("focused"),!mouseClick&&(currentFocusedElement=document.activeElement,currentFocusedElement.classList.add("focused"))},!0)}function pauseAllMedia(){document.querySelectorAll(".js-youtube").forEach(video=>{video.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}',"*")}),document.querySelectorAll(".js-vimeo").forEach(video=>{video.contentWindow.postMessage('{"method":"pause"}',"*")}),document.querySelectorAll("video").forEach(video=>video.pause()),document.querySelectorAll("product-model").forEach(model=>{model.modelViewerUI&&model.modelViewerUI.pause()})}function removeTrapFocus(elementToFocus=null){document.removeEventListener("focusin",trapFocusHandlers.focusin),document.removeEventListener("focusout",trapFocusHandlers.focusout),document.removeEventListener("keydown",trapFocusHandlers.keydown),elementToFocus&&elementToFocus.focus()}function onKeyUpEscape(event){if(event.code.toUpperCase()!=="ESCAPE")return;const openDetailsElement=event.target.closest("details[open]");if(!openDetailsElement)return;const summaryElement=openDetailsElement.querySelector("summary");openDetailsElement.removeAttribute("open"),summaryElement.setAttribute("aria-expanded",!1),summaryElement.focus()}class QuantityInput extends HTMLElement{constructor(){super(),this.input=this.querySelector("input"),this.changeEvent=new Event("change",{bubbles:!0}),this.input.addEventListener("change",this.onInputChange.bind(this)),this.querySelectorAll("button").forEach(button=>button.addEventListener("click",this.onButtonClick.bind(this)))}quantityUpdateUnsubscriber=void 0;connectedCallback(){this.validateQtyRules(),this.quantityUpdateUnsubscriber=subscribe(PUB_SUB_EVENTS.quantityUpdate,this.validateQtyRules.bind(this))}disconnectedCallback(){this.quantityUpdateUnsubscriber&&this.quantityUpdateUnsubscriber()}onInputChange(event){this.validateQtyRules()}onButtonClick(event){event.preventDefault();const previousValue=this.input.value;event.target.name==="plus"?this.input.stepUp():this.input.stepDown(),previousValue!==this.input.value&&this.input.dispatchEvent(this.changeEvent)}validateQtyRules(){const value=parseInt(this.input.value);if(this.input.min){const min=parseInt(this.input.min);this.querySelector(".quantity__button[name='minus']").classList.toggle("disabled",value<=min)}if(this.input.max){const max=parseInt(this.input.max);this.querySelector(".quantity__button[name='plus']").classList.toggle("disabled",value>=max)}}}customElements.define("quantity-input",QuantityInput);function debounce(fn,wait){let t;return(...args)=>{clearTimeout(t),t=setTimeout(()=>fn.apply(this,args),wait)}}function throttle(fn,delay){let lastCall=0;return function(...args){const now=new Date().getTime();if(!(now-lastCall<delay))return lastCall=now,fn(...args)}}function fetchConfig(type="json"){return{method:"POST",headers:{"Content-Type":"application/json",Accept:`application/${type}`}}}typeof window.Shopify>"u"&&(window.Shopify={}),Shopify.bind=function(fn,scope){return function(){return fn.apply(scope,arguments)}},Shopify.setSelectorByValue=function(selector,value){for(var i=0,count=selector.options.length;i<count;i++){var option=selector.options[i];if(value==option.value||value==option.innerHTML)return selector.selectedIndex=i,i}},Shopify.addListener=function(target,eventName,callback){target.addEventListener?target.addEventListener(eventName,callback,!1):target.attachEvent("on"+eventName,callback)},Shopify.postLink=function(path,options){options=options||{};var method=options.method||"post",params=options.parameters||{},form=document.createElement("form");form.setAttribute("method",method),form.setAttribute("action",path);for(var key in params){var hiddenField=document.createElement("input");hiddenField.setAttribute("type","hidden"),hiddenField.setAttribute("name",key),hiddenField.setAttribute("value",params[key]),form.appendChild(hiddenField)}document.body.appendChild(form),form.submit(),document.body.removeChild(form)},Shopify.CountryProvinceSelector=function(country_domid,province_domid,options){this.countryEl=document.getElementById(country_domid),this.provinceEl=document.getElementById(province_domid),this.provinceContainer=document.getElementById(options.hideElement||province_domid),Shopify.addListener(this.countryEl,"change",Shopify.bind(this.countryHandler,this)),this.initCountry(),this.initProvince()},Shopify.CountryProvinceSelector.prototype={initCountry:function(){var value=this.countryEl.getAttribute("data-default");Shopify.setSelectorByValue(this.countryEl,value),this.countryHandler()},initProvince:function(){var value=this.provinceEl.getAttribute("data-default");value&&this.provinceEl.options.length>0&&Shopify.setSelectorByValue(this.provinceEl,value)},countryHandler:function(e){var opt=this.countryEl.options[this.countryEl.selectedIndex],raw=opt.getAttribute("data-provinces"),provinces=JSON.parse(raw);if(this.clearOptions(this.provinceEl),provinces&&provinces.length==0)this.provinceContainer.style.display="none";else{for(var i=0;i<provinces.length;i++){var opt=document.createElement("option");opt.value=provinces[i][0],opt.innerHTML=provinces[i][1],this.provinceEl.appendChild(opt)}this.provinceContainer.style.display=""}},clearOptions:function(selector){for(;selector.firstChild;)selector.removeChild(selector.firstChild)},setOptions:function(selector,values){for(var i=0,count=values.length;i<values.length;i++){var opt=document.createElement("option");opt.value=values[i],opt.innerHTML=values[i],selector.appendChild(opt)}}};class MenuDrawer extends HTMLElement{constructor(){super(),this.mainDetailsToggle=this.querySelector("details"),this.addEventListener("keyup",this.onKeyUp.bind(this)),this.addEventListener("focusout",this.onFocusOut.bind(this)),this.bindEvents()}bindEvents(){this.querySelectorAll("summary").forEach(summary=>summary.addEventListener("click",this.onSummaryClick.bind(this))),this.querySelectorAll("button:not(.localization-selector):not(.country-selector__close-button):not(.country-filter__reset-button)").forEach(button=>button.addEventListener("click",this.onCloseButtonClick.bind(this)))}onKeyUp(event){if(event.code.toUpperCase()!=="ESCAPE")return;const openDetailsElement=event.target.closest("details[open]");openDetailsElement&&(openDetailsElement===this.mainDetailsToggle?this.closeMenuDrawer(event,this.mainDetailsToggle.querySelector("summary")):this.closeSubmenu(openDetailsElement))}onSummaryClick(event){const summaryElement=event.currentTarget,detailsElement=summaryElement.parentNode,parentMenuElement=detailsElement.closest(".has-submenu"),isOpen=detailsElement.hasAttribute("open"),reducedMotion=window.matchMedia("(prefers-reduced-motion: reduce)");function addTrapFocus(){trapFocus(summaryElement.nextElementSibling,detailsElement.querySelector("button")),summaryElement.nextElementSibling.removeEventListener("transitionend",addTrapFocus)}detailsElement===this.mainDetailsToggle?(isOpen&&event.preventDefault(),isOpen?this.closeMenuDrawer(event,summaryElement):this.openMenuDrawer(summaryElement),window.matchMedia("(max-width: 990px)")&&document.documentElement.style.setProperty("--viewport-height",`${window.innerHeight}px`)):setTimeout(()=>{detailsElement.classList.add("menu-opening"),summaryElement.setAttribute("aria-expanded",!0),parentMenuElement&&parentMenuElement.classList.add("submenu-open"),!reducedMotion||reducedMotion.matches?addTrapFocus():summaryElement.nextElementSibling.addEventListener("transitionend",addTrapFocus)},100)}openMenuDrawer(summaryElement){setTimeout(()=>{this.mainDetailsToggle.classList.add("menu-opening")}),summaryElement.setAttribute("aria-expanded",!0),trapFocus(this.mainDetailsToggle,summaryElement),document.body.classList.add(`overflow-hidden-${this.dataset.breakpoint}`)}closeMenuDrawer(event,elementToFocus=!1){event!==void 0&&(this.mainDetailsToggle.classList.remove("menu-opening"),this.mainDetailsToggle.querySelectorAll("details").forEach(details=>{details.removeAttribute("open"),details.classList.remove("menu-opening")}),this.mainDetailsToggle.querySelectorAll(".submenu-open").forEach(submenu=>{submenu.classList.remove("submenu-open")}),document.body.classList.remove(`overflow-hidden-${this.dataset.breakpoint}`),removeTrapFocus(elementToFocus),this.closeAnimation(this.mainDetailsToggle),event instanceof KeyboardEvent&&elementToFocus?.setAttribute("aria-expanded",!1))}onFocusOut(){setTimeout(()=>{this.mainDetailsToggle.hasAttribute("open")&&!this.mainDetailsToggle.contains(document.activeElement)&&this.closeMenuDrawer()})}onCloseButtonClick(event){const detailsElement=event.currentTarget.closest("details");this.closeSubmenu(detailsElement)}closeSubmenu(detailsElement){const parentMenuElement=detailsElement.closest(".submenu-open");parentMenuElement&&parentMenuElement.classList.remove("submenu-open"),detailsElement.classList.remove("menu-opening"),detailsElement.querySelector("summary").setAttribute("aria-expanded",!1),removeTrapFocus(detailsElement.querySelector("summary")),this.closeAnimation(detailsElement)}closeAnimation(detailsElement){let animationStart;const handleAnimation=time=>{animationStart===void 0&&(animationStart=time),time-animationStart<400?window.requestAnimationFrame(handleAnimation):(detailsElement.removeAttribute("open"),detailsElement.closest("details[open]")&&trapFocus(detailsElement.closest("details[open]"),detailsElement.querySelector("summary")))};window.requestAnimationFrame(handleAnimation)}}customElements.define("menu-drawer",MenuDrawer);class HeaderDrawer extends MenuDrawer{constructor(){super()}openMenuDrawer(summaryElement){this.header=this.header||document.querySelector(".section-header"),this.borderOffset=this.borderOffset||this.closest(".header-wrapper").classList.contains("header-wrapper--border-bottom")?1:0,document.documentElement.style.setProperty("--header-bottom-position",`${parseInt(this.header.getBoundingClientRect().bottom-this.borderOffset)}px`),this.header.classList.add("menu-open"),setTimeout(()=>{this.mainDetailsToggle.classList.add("menu-opening")}),summaryElement.setAttribute("aria-expanded",!0),window.addEventListener("resize",this.onResize),trapFocus(this.mainDetailsToggle,summaryElement),document.body.classList.add(`overflow-hidden-${this.dataset.breakpoint}`)}closeMenuDrawer(event,elementToFocus){elementToFocus&&(super.closeMenuDrawer(event,elementToFocus),this.header.classList.remove("menu-open"),window.removeEventListener("resize",this.onResize))}onResize=()=>{this.header&&document.documentElement.style.setProperty("--header-bottom-position",`${parseInt(this.header.getBoundingClientRect().bottom-this.borderOffset)}px`),document.documentElement.style.setProperty("--viewport-height",`${window.innerHeight}px`)}}customElements.define("header-drawer",HeaderDrawer);class ModalDialog extends HTMLElement{constructor(){super(),this.querySelector('[id^="ModalClose-"]').addEventListener("click",this.hide.bind(this,!1)),this.addEventListener("keyup",event=>{event.code.toUpperCase()==="ESCAPE"&&this.hide()}),this.classList.contains("media-modal")?this.addEventListener("pointerup",event=>{event.pointerType==="mouse"&&!event.target.closest("deferred-media, product-model")&&this.hide()}):this.addEventListener("click",event=>{event.target===this&&this.hide()})}connectedCallback(){this.moved||(this.moved=!0,document.body.appendChild(this))}show(opener){this.openedBy=opener;const popup=this.querySelector(".template-popup");document.body.classList.add("overflow-hidden"),this.setAttribute("open",""),popup&&popup.loadContent(),trapFocus(this,this.querySelector('[role="dialog"]')),window.pauseAllMedia()}hide(){document.body.classList.remove("overflow-hidden"),document.body.dispatchEvent(new CustomEvent("modalClosed")),this.removeAttribute("open"),removeTrapFocus(this.openedBy),window.pauseAllMedia()}}customElements.define("modal-dialog",ModalDialog);class ModalOpener extends HTMLElement{constructor(){super();const button=this.querySelector("button");button&&button.addEventListener("click",()=>{const modal=document.querySelector(this.getAttribute("data-modal"));modal&&modal.show(button)})}}customElements.define("modal-opener",ModalOpener);class DeferredMedia extends HTMLElement{constructor(){super();const poster=this.querySelector('[id^="Deferred-Poster-"]');poster&&poster.addEventListener("click",this.loadContent.bind(this))}loadContent(focus=!0){if(window.pauseAllMedia(),!this.getAttribute("loaded")){const content=document.createElement("div");content.appendChild(this.querySelector("template").content.firstElementChild.cloneNode(!0)),this.setAttribute("loaded",!0);const deferredElement=this.appendChild(content.querySelector("video, model-viewer, iframe"));focus&&deferredElement.focus(),deferredElement.nodeName=="VIDEO"&&deferredElement.getAttribute("autoplay")&&deferredElement.play()}}}customElements.define("deferred-media",DeferredMedia);class SliderComponent extends HTMLElement{constructor(){if(super(),this.slider=this.querySelector('[id^="Slider-"]'),this.sliderItems=this.querySelectorAll('[id^="Slide-"]'),this.enableSliderLooping=!1,this.currentPageElement=this.querySelector(".slider-counter--current"),this.pageTotalElement=this.querySelector(".slider-counter--total"),this.prevButton=this.querySelector('button[name="previous"]'),this.nextButton=this.querySelector('button[name="next"]'),!this.slider||!this.nextButton)return;this.initPages(),new ResizeObserver(entries=>this.initPages()).observe(this.slider),this.slider.addEventListener("scroll",this.update.bind(this)),this.prevButton.addEventListener("click",this.onButtonClick.bind(this)),this.nextButton.addEventListener("click",this.onButtonClick.bind(this))}initPages(){this.sliderItemsToShow=Array.from(this.sliderItems).filter(element=>element.clientWidth>0),!(this.sliderItemsToShow.length<2)&&(this.sliderItemOffset=this.sliderItemsToShow[1].offsetLeft-this.sliderItemsToShow[0].offsetLeft,this.slidesPerPage=Math.floor((this.slider.clientWidth-this.sliderItemsToShow[0].offsetLeft)/this.sliderItemOffset),this.totalPages=this.sliderItemsToShow.length-this.slidesPerPage+1,this.update())}resetPages(){this.sliderItems=this.querySelectorAll('[id^="Slide-"]'),this.initPages()}update(){if(!this.slider||!this.nextButton)return;const previousPage=this.currentPage;this.currentPage=Math.round(this.slider.scrollLeft/this.sliderItemOffset)+1,this.currentPageElement&&this.pageTotalElement&&(this.currentPageElement.textContent=this.currentPage,this.pageTotalElement.textContent=this.totalPages),this.currentPage!=previousPage&&this.dispatchEvent(new CustomEvent("slideChanged",{detail:{currentPage:this.currentPage,currentElement:this.sliderItemsToShow[this.currentPage-1]}})),!this.enableSliderLooping&&(this.isSlideVisible(this.sliderItemsToShow[0])&&this.slider.scrollLeft===0?this.prevButton.setAttribute("disabled","disabled"):this.prevButton.removeAttribute("disabled"),this.isSlideVisible(this.sliderItemsToShow[this.sliderItemsToShow.length-1])?this.nextButton.setAttribute("disabled","disabled"):this.nextButton.removeAttribute("disabled"))}isSlideVisible(element,offset=0){const lastVisibleSlide=this.slider.clientWidth+this.slider.scrollLeft-offset;return element.offsetLeft+element.clientWidth<=lastVisibleSlide&&element.offsetLeft>=this.slider.scrollLeft}onButtonClick(event){event.preventDefault();const step=event.currentTarget.dataset.step||1;this.slideScrollPosition=event.currentTarget.name==="next"?this.slider.scrollLeft+step*this.sliderItemOffset:this.slider.scrollLeft-step*this.sliderItemOffset,this.setSlidePosition(this.slideScrollPosition)}setSlidePosition(position){this.slider.scrollTo({left:position})}}customElements.define("slider-component",SliderComponent);class SlideshowComponent extends SliderComponent{constructor(){super(),this.sliderControlWrapper=this.querySelector(".slider-buttons"),this.enableSliderLooping=!0,this.sliderControlWrapper&&(this.sliderFirstItemNode=this.slider.querySelector(".slideshow__slide"),this.sliderItemsToShow.length>0&&(this.currentPage=1),this.announcementBarSlider=this.querySelector(".announcement-bar-slider"),this.announcerBarAnimationDelay=this.announcementBarSlider?250:0,this.sliderControlLinksArray=Array.from(this.sliderControlWrapper.querySelectorAll(".slider-counter__link")),this.sliderControlLinksArray.forEach(link=>link.addEventListener("click",this.linkToSlide.bind(this))),this.slider.addEventListener("scroll",this.setSlideVisibility.bind(this)),this.setSlideVisibility(),this.announcementBarSlider&&(this.announcementBarArrowButtonWasClicked=!1,this.reducedMotion=window.matchMedia("(prefers-reduced-motion: reduce)"),this.reducedMotion.addEventListener("change",()=>{this.slider.getAttribute("data-autoplay")==="true"&&this.setAutoPlay()}),[this.prevButton,this.nextButton].forEach(button=>{button.addEventListener("click",()=>{this.announcementBarArrowButtonWasClicked=!0},{once:!0})})),this.slider.getAttribute("data-autoplay")==="true"&&this.setAutoPlay())}setAutoPlay(){this.autoplaySpeed=this.slider.dataset.speed*1e3,this.addEventListener("mouseover",this.focusInHandling.bind(this)),this.addEventListener("mouseleave",this.focusOutHandling.bind(this)),this.addEventListener("focusin",this.focusInHandling.bind(this)),this.addEventListener("focusout",this.focusOutHandling.bind(this)),this.querySelector(".slideshow__autoplay")?(this.sliderAutoplayButton=this.querySelector(".slideshow__autoplay"),this.sliderAutoplayButton.addEventListener("click",this.autoPlayToggle.bind(this)),this.autoplayButtonIsSetToPlay=!0,this.play()):this.reducedMotion.matches||this.announcementBarArrowButtonWasClicked?this.pause():this.play()}onButtonClick(event){super.onButtonClick(event),this.wasClicked=!0;const isFirstSlide=this.currentPage===1,isLastSlide=this.currentPage===this.sliderItemsToShow.length;if(!isFirstSlide&&!isLastSlide){this.applyAnimationToAnnouncementBar(event.currentTarget.name);return}isFirstSlide&&event.currentTarget.name==="previous"?this.slideScrollPosition=this.slider.scrollLeft+this.sliderFirstItemNode.clientWidth*this.sliderItemsToShow.length:isLastSlide&&event.currentTarget.name==="next"&&(this.slideScrollPosition=0),this.setSlidePosition(this.slideScrollPosition),this.applyAnimationToAnnouncementBar(event.currentTarget.name)}setSlidePosition(position){this.setPositionTimeout&&clearTimeout(this.setPositionTimeout),this.setPositionTimeout=setTimeout(()=>{this.slider.scrollTo({left:position})},this.announcerBarAnimationDelay)}update(){super.update(),this.sliderControlButtons=this.querySelectorAll(".slider-counter__link"),this.prevButton.removeAttribute("disabled"),this.sliderControlButtons.length&&(this.sliderControlButtons.forEach(link=>{link.classList.remove("slider-counter__link--active"),link.removeAttribute("aria-current")}),this.sliderControlButtons[this.currentPage-1].classList.add("slider-counter__link--active"),this.sliderControlButtons[this.currentPage-1].setAttribute("aria-current",!0))}autoPlayToggle(){this.togglePlayButtonState(this.autoplayButtonIsSetToPlay),this.autoplayButtonIsSetToPlay?this.pause():this.play(),this.autoplayButtonIsSetToPlay=!this.autoplayButtonIsSetToPlay}focusOutHandling(event){if(this.sliderAutoplayButton){const focusedOnAutoplayButton=event.target===this.sliderAutoplayButton||this.sliderAutoplayButton.contains(event.target);if(!this.autoplayButtonIsSetToPlay||focusedOnAutoplayButton)return;this.play()}else!this.reducedMotion.matches&&!this.announcementBarArrowButtonWasClicked&&this.play()}focusInHandling(event){this.sliderAutoplayButton?(event.target===this.sliderAutoplayButton||this.sliderAutoplayButton.contains(event.target))&&this.autoplayButtonIsSetToPlay?this.play():this.autoplayButtonIsSetToPlay&&this.pause():this.announcementBarSlider.contains(event.target)&&this.pause()}play(){this.slider.setAttribute("aria-live","off"),clearInterval(this.autoplay),this.autoplay=setInterval(this.autoRotateSlides.bind(this),this.autoplaySpeed)}pause(){this.slider.setAttribute("aria-live","polite"),clearInterval(this.autoplay)}togglePlayButtonState(pauseAutoplay){pauseAutoplay?(this.sliderAutoplayButton.classList.add("slideshow__autoplay--paused"),this.sliderAutoplayButton.setAttribute("aria-label",window.accessibilityStrings.playSlideshow)):(this.sliderAutoplayButton.classList.remove("slideshow__autoplay--paused"),this.sliderAutoplayButton.setAttribute("aria-label",window.accessibilityStrings.pauseSlideshow))}autoRotateSlides(){const slideScrollPosition=this.currentPage===this.sliderItems.length?0:this.slider.scrollLeft+this.sliderItemOffset;this.setSlidePosition(slideScrollPosition),this.applyAnimationToAnnouncementBar()}setSlideVisibility(event){this.sliderItemsToShow.forEach((item,index)=>{const linkElements=item.querySelectorAll("a");index===this.currentPage-1?(linkElements.length&&linkElements.forEach(button=>{button.removeAttribute("tabindex")}),item.setAttribute("aria-hidden","false"),item.removeAttribute("tabindex")):(linkElements.length&&linkElements.forEach(button=>{button.setAttribute("tabindex","-1")}),item.setAttribute("aria-hidden","true"),item.setAttribute("tabindex","-1"))}),this.wasClicked=!1}applyAnimationToAnnouncementBar(button="next"){if(!this.announcementBarSlider)return;const itemsCount=this.sliderItems.length,increment=button==="next"?1:-1,currentIndex=this.currentPage-1;let nextIndex=(currentIndex+increment)%itemsCount;nextIndex=nextIndex===-1?itemsCount-1:nextIndex;const nextSlide=this.sliderItems[nextIndex],currentSlide=this.sliderItems[currentIndex],animationClassIn="announcement-bar-slider--fade-in",animationClassOut="announcement-bar-slider--fade-out",isFirstSlide=currentIndex===0,isLastSlide=currentIndex===itemsCount-1,direction=button==="next"&&!isLastSlide||button==="previous"&&isFirstSlide?"next":"previous";currentSlide.classList.add(`${animationClassOut}-${direction}`),nextSlide.classList.add(`${animationClassIn}-${direction}`),setTimeout(()=>{currentSlide.classList.remove(`${animationClassOut}-${direction}`),nextSlide.classList.remove(`${animationClassIn}-${direction}`)},this.announcerBarAnimationDelay*2)}linkToSlide(event){event.preventDefault();const slideScrollPosition=this.slider.scrollLeft+this.sliderFirstItemNode.clientWidth*(this.sliderControlLinksArray.indexOf(event.currentTarget)+1-this.currentPage);this.slider.scrollTo({left:slideScrollPosition})}}customElements.define("slideshow-component",SlideshowComponent);class VariantSelects extends HTMLElement{constructor(){super(),this.addEventListener("change",this.onVariantChange)}onVariantChange(event){this.updateOptions(),this.updateMasterId(),this.updateSelectedSwatchValue(event),this.toggleAddButton(!0,"",!1),this.updatePickupAvailability(),this.removeErrorMessage(),this.updateVariantStatuses(),this.currentVariant?(this.updateMedia(),this.updateURL(),this.updateVariantInput(),this.renderProductInfo(),this.updateShareUrl()):(this.toggleAddButton(!0,"",!0),this.setUnavailable())}updateOptions(){this.options=Array.from(this.querySelectorAll("select, fieldset"),element=>{if(element.tagName==="SELECT")return element.value;if(element.tagName==="FIELDSET")return Array.from(element.querySelectorAll("input")).find(radio=>radio.checked)?.value})}updateMasterId(){this.currentVariant=this.getVariantData().find(variant=>!variant.options.map((option,index)=>this.options[index]===option).includes(!1))}updateSelectedSwatchValue({target}){const{name,value,tagName}=target;if(tagName==="SELECT"&&target.selectedOptions.length){const swatchValue=target.selectedOptions[0].dataset.optionSwatchValue,selectedDropdownSwatchValue=this.querySelector(`[data-selected-dropdown-swatch="${name}"] > .swatch`);if(!selectedDropdownSwatchValue)return;swatchValue?(selectedDropdownSwatchValue.style.setProperty("--swatch--background",swatchValue),selectedDropdownSwatchValue.classList.remove("swatch--unavailable")):(selectedDropdownSwatchValue.style.setProperty("--swatch--background","unset"),selectedDropdownSwatchValue.classList.add("swatch--unavailable"))}else if(tagName==="INPUT"&&target.type==="radio"){const selectedSwatchValue=this.querySelector(`[data-selected-swatch-value="${name}"]`);selectedSwatchValue&&(selectedSwatchValue.innerHTML=value)}}updateMedia(){if(!this.currentVariant||!this.currentVariant.featured_media)return;document.querySelectorAll(`[id^="MediaGallery-${this.dataset.section}"]`).forEach(mediaGallery=>mediaGallery.setActiveMedia(`${this.dataset.section}-${this.currentVariant.featured_media.id}`,!0));const modalContent=document.querySelector(`#ProductModal-${this.dataset.section} .product-media-modal__content`);if(!modalContent)return;const newMediaModal=modalContent.querySelector(`[data-media-id="${this.currentVariant.featured_media.id}"]`);modalContent.prepend(newMediaModal)}updateURL(){!this.currentVariant||this.dataset.updateUrl==="false"||window.history.replaceState({},"",`${this.dataset.url}?variant=${this.currentVariant.id}`)}updateShareUrl(){const shareButton=document.getElementById(`Share-${this.dataset.section}`);!shareButton||!shareButton.updateUrl||shareButton.updateUrl(`${window.shopUrl}${this.dataset.url}?variant=${this.currentVariant.id}`)}updateVariantInput(){document.querySelectorAll(`#product-form-${this.dataset.section}, #product-form-installment-${this.dataset.section}`).forEach(productForm=>{const input=productForm.querySelector('input[name="id"]');input.value=this.currentVariant.id,input.dispatchEvent(new Event("change",{bubbles:!0}))})}updateVariantStatuses(){const selectedOptionOneVariants=this.variantData.filter(variant=>this.querySelector(":checked").value===variant.option1),inputWrappers=[...this.querySelectorAll(".product-form__input")];inputWrappers.forEach((option,index)=>{if(index===0)return;const optionInputs=[...option.querySelectorAll('input[type="radio"], option')],previousOptionSelected=inputWrappers[index-1].querySelector(":checked").value,availableOptionInputsValue=selectedOptionOneVariants.filter(variant=>variant.available&&variant[`option${index}`]===previousOptionSelected).map(variantOption=>variantOption[`option${index+1}`]);this.setInputAvailability(optionInputs,availableOptionInputsValue)})}setInputAvailability(elementList,availableValuesList){elementList.forEach(element=>{const value=element.getAttribute("value"),availableElement=availableValuesList.includes(value);element.tagName==="INPUT"?element.classList.toggle("disabled",!availableElement):element.tagName==="OPTION"&&(element.innerText=availableElement?value:window.variantStrings.unavailable_with_option.replace("[value]",value))})}updatePickupAvailability(){const pickUpAvailability=document.querySelector("pickup-availability");pickUpAvailability&&(this.currentVariant&&this.currentVariant.available?pickUpAvailability.fetchAvailability(this.currentVariant.id):(pickUpAvailability.removeAttribute("available"),pickUpAvailability.innerHTML=""))}removeErrorMessage(){const section=this.closest("section");if(!section)return;const productForm=section.querySelector("product-form");productForm&&productForm.handleErrorMessage()}renderProductInfo(){const requestedVariantId=this.currentVariant.id,sectionId=this.dataset.originalSection?this.dataset.originalSection:this.dataset.section;fetch(`${this.dataset.url}?variant=${requestedVariantId}&section_id=${this.dataset.originalSection?this.dataset.originalSection:this.dataset.section}`).then(response=>response.text()).then(responseText=>{if(this.currentVariant.id!==requestedVariantId)return;const html=new DOMParser().parseFromString(responseText,"text/html"),destination=document.getElementById(`price-${this.dataset.section}`),source=html.getElementById(`price-${this.dataset.originalSection?this.dataset.originalSection:this.dataset.section}`),skuSource=html.getElementById(`Sku-${this.dataset.originalSection?this.dataset.originalSection:this.dataset.section}`),skuDestination=document.getElementById(`Sku-${this.dataset.section}`),inventorySource=html.getElementById(`Inventory-${this.dataset.originalSection?this.dataset.originalSection:this.dataset.section}`),inventoryDestination=document.getElementById(`Inventory-${this.dataset.section}`),volumePricingSource=html.getElementById(`Volume-${this.dataset.originalSection?this.dataset.originalSection:this.dataset.section}`),pricePerItemDestination=document.getElementById(`Price-Per-Item-${this.dataset.section}`),pricePerItemSource=html.getElementById(`Price-Per-Item-${this.dataset.originalSection?this.dataset.originalSection:this.dataset.section}`),volumePricingDestination=document.getElementById(`Volume-${this.dataset.section}`),qtyRules=document.getElementById(`Quantity-Rules-${this.dataset.section}`),volumeNote=document.getElementById(`Volume-Note-${this.dataset.section}`);volumeNote&&volumeNote.classList.remove("hidden"),volumePricingDestination&&volumePricingDestination.classList.remove("hidden"),qtyRules&&qtyRules.classList.remove("hidden"),source&&destination&&(destination.innerHTML=source.innerHTML),inventorySource&&inventoryDestination&&(inventoryDestination.innerHTML=inventorySource.innerHTML),skuSource&&skuDestination&&(skuDestination.innerHTML=skuSource.innerHTML,skuDestination.classList.toggle("hidden",skuSource.classList.contains("hidden"))),volumePricingSource&&volumePricingDestination&&(volumePricingDestination.innerHTML=volumePricingSource.innerHTML),pricePerItemSource&&pricePerItemDestination&&(pricePerItemDestination.innerHTML=pricePerItemSource.innerHTML,pricePerItemDestination.classList.toggle("hidden",pricePerItemSource.classList.contains("hidden")));const price=document.getElementById(`price-${this.dataset.section}`);price&&price.classList.remove("hidden"),inventoryDestination&&inventoryDestination.classList.toggle("hidden",inventorySource.innerText==="");const addButtonUpdated=html.getElementById(`ProductSubmitButton-${sectionId}`);this.toggleAddButton(addButtonUpdated?addButtonUpdated.hasAttribute("disabled"):!0,window.variantStrings.soldOut),publish(PUB_SUB_EVENTS.variantChange,{data:{sectionId,html,variant:this.currentVariant}})})}toggleAddButton(disable=!0,text,modifyClass=!0){const productForm=document.getElementById(`product-form-${this.dataset.section}`);if(!productForm)return;const addButton=productForm.querySelector('[name="add"]'),addButtonText=productForm.querySelector('[name="add"] > span');addButton&&(disable?(addButton.setAttribute("disabled","disabled"),text&&(addButtonText.textContent=text)):(addButton.removeAttribute("disabled"),addButtonText.textContent=window.variantStrings.addToCart))}setUnavailable(){const button=document.getElementById(`product-form-${this.dataset.section}`),addButton=button.querySelector('[name="add"]'),addButtonText=button.querySelector('[name="add"] > span'),price=document.getElementById(`price-${this.dataset.section}`),inventory=document.getElementById(`Inventory-${this.dataset.section}`),sku=document.getElementById(`Sku-${this.dataset.section}`),pricePerItem=document.getElementById(`Price-Per-Item-${this.dataset.section}`),volumeNote=document.getElementById(`Volume-Note-${this.dataset.section}`),volumeTable=document.getElementById(`Volume-${this.dataset.section}`),qtyRules=document.getElementById(`Quantity-Rules-${this.dataset.section}`);addButton&&(addButtonText.textContent=window.variantStrings.unavailable,price&&price.classList.add("hidden"),inventory&&inventory.classList.add("hidden"),sku&&sku.classList.add("hidden"),pricePerItem&&pricePerItem.classList.add("hidden"),volumeNote&&volumeNote.classList.add("hidden"),volumeTable&&volumeTable.classList.add("hidden"),qtyRules&&qtyRules.classList.add("hidden"))}getVariantData(){return this.variantData=this.variantData||JSON.parse(this.querySelector('[type="application/json"]').textContent),this.variantData}}customElements.define("variant-selects",VariantSelects);class ProductRecommendations extends HTMLElement{constructor(){super()}connectedCallback(){const handleIntersection=(entries,observer)=>{entries[0].isIntersecting&&(observer.unobserve(this),fetch(this.dataset.url).then(response=>response.text()).then(text=>{const html=document.createElement("div");html.innerHTML=text;const recommendations=html.querySelector("product-recommendations");recommendations&&recommendations.innerHTML.trim().length&&(this.innerHTML=recommendations.innerHTML),!this.querySelector("slideshow-component")&&this.classList.contains("complementary-products")&&this.remove(),html.querySelector(".grid__item")&&this.classList.add("product-recommendations--loaded")}).catch(e=>{console.error(e)}))};new IntersectionObserver(handleIntersection.bind(this),{rootMargin:"0px 0px 400px 0px"}).observe(this)}}customElements.define("product-recommendations",ProductRecommendations);
//# sourceMappingURL=/cdn/shop/t/1/assets/global.js.map?v=37284204640041572741707074513
