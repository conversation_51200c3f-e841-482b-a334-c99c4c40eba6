/*!
 * Star Rating Portugese Brazilian Translations
 *
 * This file must be loaded after 'star-rating.js'. Patterns in braces '{}', or
 * any HTML markup tags in the messages must not be converted or translated.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 *
 * @see http://github.com/kartik-v/bootstrap-star-rating
 * <AUTHOR> <<EMAIL>>
 */
(function ($) {
    "use strict";
    $.fn.ratingLocales['pt-BR'] = {
        defaultCaption: '{rating} Estrelas',
        starCaptions: {
            0.5: 'Mei<PERSON> Estrela',
            1: 'Uma Estrela',
            1.5: 'Uma Estrela e Mei<PERSON>',
            2: '<PERSON>as Estrel<PERSON>',
            2.5: '<PERSON><PERSON> Estrel<PERSON> e <PERSON>',
            3: 'Tr<PERSON><PERSON> Estrel<PERSON>',
            3.5: 'Tr<PERSON><PERSON> Estrelas e Mei<PERSON>',
            4: 'Quatro Estrelas',
            4.5: 'Quatro Estrelas e Mei<PERSON>',
            5: '<PERSON><PERSON><PERSON>'
        },
        clearButtonTitle: 'Lim<PERSON>',
        clearCaption: 'Não Avaliado'
    };
})(window.jQuery);
