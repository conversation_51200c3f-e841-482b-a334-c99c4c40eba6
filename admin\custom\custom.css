/* width */
.form-card ::-webkit-scrollbar {
    width: 5px;
}

/* Track */
.form-card ::-webkit-scrollbar-track {
    border-radius: 10px;
}

/* Handle */
.form-card ::-webkit-scrollbar-thumb {
    background: none;
    border-radius: 10px;
}

/* Handle on hover */
.form-card ::-webkit-scrollbar-thumb:hover {
    background: none;
}

.login-page {
    /* background-image: url('../images/eshop_img.jpg'); */
    /* background-image: url('/assets/admin/images/eshop_img.jpg');  */
    height: 100% !important;
}

.text-middle-line {
    width: 100%;
    text-align: center;
    border-bottom: 1px solid #000;
    line-height: 0.1em;
    margin: 10px 0 20px;
}

.text-middle-line span {
    background: #fff;
    padding: 0 10px;
}

hr.horizontal.light {
    background-image: linear-gradient(90deg, hsl(0deg 100% 49.21% / 0%), #fff, hsl(0deg 0% 100% / 0%));
    background-color: #131313;
}

@media (min-width: 375px) {

    #seller_mobile,
    #delivery_boy_mobile {
        width: 142%;
    }
}

@media (min-width: 375px) {

    #seller_mobile,
    #delivery_boy_mobile {
        width: 100%;
    }
}

@media (min-width: 425px) {

    #seller_mobile,
    #delivery_boy_mobile {
        width: 107%;
    }

    #delivery_boy_mobile {
        width: 114%;
    }
}

@media (min-width: 768px) {
    #seller_mobile {
        width: 143%;
    }

    #delivery_boy_mobile {
        width: 152%;
    }
}

@media (min-width: 1000px) {
    #delivery_boy_mobile {
        width: 152%;
    }
}

.login-page-bg {
    max-height: 100%;
    max-width: 100%;
    /* display: flex; */
    overflow: hidden;
}

.login-page-bg img {
    /* height: 100%; */
    max-width: 100%;
    background-size: cover;
    object-fit: contain;
}

.login-page>.alert {
    top: 10px;
    position: absolute;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.login-card-body {
    border-radius: 14px;
    background: #f1f1f1 !important;
    padding: 35px !important;
}

.form-box {
    position: absolute;
    top: 18px;
    right: 25px;
}

@media only screen and (min-width: 630px) {
    .login-box {
        position: absolute;
        width: 520px;
        right: 120px;
    }
}

@media only screen and (min-width: 430px) {
    .login-box {
        position: absolute;
        /*width: 520px;*/
        /*right: 120px;*/
    }
}

@media only screen and (max-width: 430px) {

    .login-box {
        position: absolute;
        font-size: 12px;
        /* width: 520px;
        right: 120px; */
    }

    .login-logo img {
        height: 73px;
    }
    .form-box {
        position: absolute;
        top: 18px;
        right: 0px;
    }
    .login-card-body h2 {
        font-size: 20px;
    }
}


.login-card-body h2 {
    font-weight: bolder;
}

.login-card-body .form-control {
    /* padding: 1.375rem 3.75rem !important; */
    padding: 1.375rem 1.75rem !important;
    ;
}

.btn-signin {
    /* background-color: #f78b77; */
    background-color: #ff7264;
    color: white;
}

.btn-signin:hover {
    color: #ffffff;
    text-decoration: none;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
    border-radius: inherit;
}

body {
    font-family: 'Poppins', sans-serif;
    top: 0px !important;
}

.active>i.nav-icon {
    color: white !important;
}

.msg_error {
    color: #D8000C;
    background-color: #FFD2D2;
}

.msg_error>p:last-child,
.iziToast-message>p:last-child {
    margin-bottom: 0;
}

.msg_success {
    color: #4F8A10;
    background-color: #DFF2BF;
}

.info {
    background-color: #e7f3fe;
    border-left: 6px solid #2196F3;
}

.level-1 {
    padding-left: 12px;
}

.awaiting-box {
    background-color: #5989b3 !important;
}

.received-box {
    background-color: #9071a3 !important;
}

.processed-box {
    background-color: #368c9a !important;
}

.shipped-box {
    background-color: #af7a92 !important;
}

.VIpgJd-ZVi9od-ORHb-OEVmcd {
    display: none;
}

.VIpgJd-ZVi9od-l4eHX-hSRGPd {
    display: none;
}

.delivered-box {
    background-color: #72978c !important;
}

.sales-tab .nav-link.active,
.sales-tab .show>.nav-link {
    background-color: #2a5583 !important;
}

.details-box {
    background-color: #649ca5 !important;
}

.card {
    box-shadow: 0 0 1px rgb(0 0 0 / 7%), 0 1px 3px rgb(0 0 0 / 10%) !important;
}

.card_seller {
    background-color: #ffffff !important;
    /* height: 100%; */
    max-height: 97vh;
    overflow-y: auto;
}

.total-info-box {
    background-color: #606b8b !important;
}

.sold-products {
    color: #fff;
    background-color: #c44674 !important;
}

#loading {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    position: fixed;
    display: block;
    opacity: 0.7;
    background-color: rgb(26, 26, 27);
    z-index: 9999;
    text-align: center;
}

.lds-ring {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
    top: 48%;
}

.lds-ring div {
    box-sizing: border-box;
    display: block;
    position: absolute;
    width: 64px;
    height: 64px;
    margin: 8px;
    border: 2px solid #fff;
    border-radius: 50%;
    animation: lds-ring 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
    border-color: #fff transparent transparent transparent;
}

.lds-ring div:nth-child(1) {
    animation-delay: -0.45s;
}

.lds-ring div:nth-child(2) {
    animation-delay: -0.3s;
}

.lds-ring div:nth-child(3) {
    animation-delay: -0.15s;
}

@keyframes lds-ring {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.pro_loose {
    display: none;
}

.move {
    cursor: move;
}

.view input {
    pointer-events: none;
}

.view select {
    pointer-events: none;
}

.view .btn {
    display: none;
}

.custom-checkbox {
    transform: scale(2);
}

.action-btn {
    min-width: 27px;
    max-width: 27px;
}

.view .card-footer {
    display: none !important;
}

.grow img {
    transition: ease;
    transition-duration: 150ms;
}

.grow img:hover {
    -webkit-transform: scale(1.05);
    -ms-transform: scale(1.05);
    transform: scale(1.05);
    transition: ease;
    transition-duration: 150ms;
}

.time-line-box {
    /*height: 10px; */
    padding: 10px 0 36px 0;
    width: 100%;
}

.time-line-box .timeline {
    list-style-type: none;
    display: flex;
    padding: 0;
    text-align: center;
}

.time-line-box .timestamp {
    margin: auto;
    margin-bottom: 5px;
    padding: 0px 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.time-line-box .status {
    padding: 0px 10px;
    display: flex;
    justify-content: center;
    border-top: 3px solid #455EFC;
    position: relative;
    transition: all 200ms ease-in;
}

.time-line-box .status span {
    padding-top: 8px;
}

.time-line-box .status span:before {
    content: '';
    width: 12px;
    height: 12px;
    background-color: #455EFC;
    border-radius: 12px;
    border: 2px solid #455EFC;
    position: absolute;
    left: 50%;
    top: 0%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    transition: all 200ms ease-in;
}

.swiper-container {
    width: 95%;
    margin: auto;
    overflow-y: auto;
}

.swiper-wrapper {
    display: inline-flex;
    flex-direction: row;
    overflow-y: auto;
    justify-content: center;
}

.swiper-container::-webkit-scrollbar-track {
    background: #a8a8a8b6;
}

.swiper-container::-webkit-scrollbar {
    height: 2px;
}

.swiper-container::-webkit-scrollbar-thumb {
    background: #4F4F4F !important;
}

.swiper-slide {
    text-align: center;
    font-size: 12px;
    width: 200px;
    height: 100%;
    position: relative;
}

.image-upload-div {
    align-content: center;
    display: flex;
    height: 100%;
    justify-content: center;
    overflow: hidden;
    width: 100% !important;
}

.image-upload-div img {
    align-content: center;
    display: flex;
    /* height: 200px; */
    justify-content: center;
    overflow: hidden;
    /* width: 100% !important; */
}

.product-image {
    max-width: 80px !important;
}

.driving-license-image {
    max-width: 200px !important;
}

/*
.printme {
  display: none;
}*/

@media print {
    body * {
        visibility: hidden;
    }

    #section-not-to-print,
    #section-not-to-print * {
        display: none;
    }

    #section-to-print,
    #section-to-print * {
        visibility: visible;
    }

    #section-to-print {
        position: absolute;
        left: 0;
        top: 0;
    }
}

.preview-thumbnail.nav-tabs {
    border: none;
    margin-top: 15px;
}

.preview-thumbnail.nav-tabs li {
    width: 18%;
    margin-right: 2.5%;
}

.preview-thumbnail.nav-tabs li img {
    max-width: 100%;
    display: block;
    margin-top: 4px;
}

.preview-thumbnail.nav-tabs li a {
    padding: 0;
    margin: 0;
}

.preview-thumbnail.nav-tabs li:last-of-type {
    margin-right: 0;
}

.l1 {
    padding-left: 10px;
}

.l2 {
    padding-left: 20px;
}

.l3 {
    padding-left: 40px;
}

.l4 {
    padding-left: 60px;
}

.l5 {
    padding-left: 80px;
}

.l6 {
    padding-left: 90px;
}

.l7 {
    padding-left: 90px;
}

.os-theme-light>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle {
    background: rgb(2 2 2 / 40%) !important;
}

.text_editor {
    width: 100%;
    height: 200px;
    font-size: 14px;
    line-height: 18px;
    border: 1px solid #dddddd;
    padding: 10px;
}

.w-10px {
    width: 10px
}

.chart-height {
    height: 439.117px;
}

.piechat_height {
    width: 100%;
    height: 350px;
}

.category-tree-container {
    height: 250px;
    overflow-y: scroll;
}

.order-container {
    overflow: scroll;
    max-height: 500px;
}

.variants_process .form-control:disabled,
.form-control[readonly] {
    background-color: #ffffff;
    opacity: 1;
}

.icon-link-remove {
    position: absolute;
    z-index: 1;
    right: 0;
    background: white;
    color: #ff0000;
}

.login-logo a img {
    max-height: 125px;
}

.error {
    color: #D8000C;
}

.modal {
    overflow-y: auto;
}

.cart-product-image {
    display: block;
    margin-bottom: 0;
    vertical-align: middle;
    height: 130px;
    width: 130px;
}

.order-product-image {
    display: block;
    margin-bottom: 0;
    vertical-align: middle;
    height: 58px;
}

.direct-chat-text {
    width: fit-content;
}

.right .direct-chat-text {
    float: right;
}

.direct-chat-timestamp {
    margin: 0 10px;
}

.direct-chat-text {
    margin: 5px 0 0 10px;
}

.right .direct-chat-text {
    margin-right: 10px;
}

.direct-chat-messages {
    height: 384px;
}

.asterik {
    font-size: 20px;
    line-height: 0px;
    vertical-align: middle;
}

.tox .tox-menubar {
    background-color: #e7e8e7;
    display: flex;
    flex: 0 0 auto;
    flex-shrink: 0;
    flex-wrap: wrap;
    padding: 0 4px 0 4px;
}

.tox .tox-notification--warn,
.tox .tox-notification--warning {
    background-color: #fffaea;
    border-color: #ffe89d;
    color: #222f3e;
    display: none !important;
}

.detail-icon {
    color: #000000;
}

.detail-icon:hover {
    color: #000000;
}


/* google translate */

.goog-te-combo {
    position: relative;
    width: 200px;
    padding: 10px;
    margin: 0 auto;
    border-radius: 25px;
    background: #dceaf2;
    color: black;
    outline: none;
    cursor: pointer;
    font-weight: bold;
}

.goog-te-banner-frame.skiptranslate {
    display: none !important;
}

.goog-logo-link {
    display: none !important;
}

.goog-te-gadget {
    color: transparent !important;
}

/* end google translate */

/* .shop-item-image {
    height: 220px;
    line-height: 220px;
    width: 650px;
    max-width: 100%;
    align-items: center;
} */
.title-link {
    /* height: 45px;
    display: block; */
    overflow: hidden;
    position: relative;
    transition: all 150ms ease-in;
}

.title_wrap {
    text-wrap: nowrap !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
}

.title-link:hover {
    height: 100% !important;
    /* transition: 150ms; */
}

.title-overlay {
    z-index: 5;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    /* background: linear-gradient(-1deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 12%, rgba(0, 212, 255, 0) 100%); */
    /* transition: all 150ms ease-in; */
}

.title-overlay:hover {
    background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 0%, rgba(0, 212, 255, 0) 100%);
}

.shop-item-image {
    /* height: 220px; */
    height: 190px;
    width: 100%;
}

.shop-item-image img {
    max-height: 100%;
    max-width: 100%;
}

img.item-image {
    max-width: 100%;
    max-height: 100%;
    height: fit-content;
}

.cart-image {
    width: 80px;
    height: 80px;
}

.cart-items {
    max-height: 400px;
    overflow: auto;
}

.cart-image img {
    max-width: 100%;
    max-height: 100%;
}

.clr-picker {
    display: none;
    flex-wrap: wrap;
    position: absolute;
    width: 200px;
    z-index: 1000;
    border-radius: 10px;
    background-color: #fff;
    justify-content: space-between;
    box-shadow: 0 0 5px rgba(0, 0, 0, .05), 0 5px 20px rgba(0, 0, 0, .1);
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
}

.clr-picker.clr-open {
    display: flex;
}

.clr-picker[data-alpha="false"] .clr-alpha {
    display: none;
}

.clr-gradient {
    position: relative;
    width: 100%;
    height: 100px;
    margin-bottom: 15px;
    border-radius: 3px 3px 0 0;
    background-image: linear-gradient(rgba(0, 0, 0, 0), #000), linear-gradient(90deg, #fff, currentColor);
    cursor: pointer;
}

.clr-marker {
    position: absolute;
    width: 12px;
    height: 12px;
    margin: -6px 0 0 -6px;
    border: 1px solid #fff;
    border-radius: 50%;
    background-color: currentColor;
    cursor: pointer;
}

.clr-picker input[type="range"]::-webkit-slider-runnable-track {
    width: 100%;
    height: 8px;
}

.clr-picker input[type="range"]::-webkit-slider-thumb {
    width: 8px;
    height: 8px;
    -webkit-appearance: none;
}

.clr-picker input[type="range"]::-moz-range-track {
    width: 100%;
    height: 8px;
    border: 0;
}

.clr-picker input[type="range"]::-moz-range-thumb {
    width: 8px;
    height: 8px;
    border: 0;
}

.clr-hue {
    background-image: linear-gradient(to right, #f00 0%, #ff0 16.66%, #0f0 33.33%, #0ff 50%, #00f 66.66%, #f0f 83.33%, #f00 100%);
}

.clr-hue,
.clr-alpha {
    position: relative;
    width: calc(100% - 40px);
    height: 8px;
    margin: 5px 20px;
    border-radius: 4px;
}

.clr-alpha span {
    display: block;
    height: 100%;
    width: 100%;
    border-radius: inherit;
    background-image: linear-gradient(90deg, rgba(0, 0, 0, 0), currentColor);
}

.clr-hue input,
.clr-alpha input {
    position: absolute;
    width: calc(100% + 16px);
    height: 16px;
    left: -8px;
    top: -4px;
    margin: 0;
    background-color: transparent;
    opacity: 0;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
}

.clr-hue div,
.clr-alpha div {
    position: absolute;
    width: 16px;
    height: 16px;
    left: 0;
    top: 50%;
    margin-left: -8px;
    transform: translateY(-50%);
    border: 2px solid #fff;
    border-radius: 50%;
    background-color: currentColor;
    box-shadow: 0 0 1px #888;
    pointer-events: none;
}

.clr-alpha div:before {
    content: '';
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    border-radius: 50%;
    background-color: currentColor;
}

.clr-format {
    display: none;
    order: 1;
    width: calc(100% - 40px);
    margin: 0 20px 20px;
}

.clr-segmented {
    display: flex;
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 15px;
    box-sizing: border-box;
    color: #999;
    font-size: 12px;
}

.clr-segmented input,
.clr-segmented legend {
    position: absolute;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    border: 0;
    left: 0;
    top: 0;
    opacity: 0;
    pointer-events: none;
}

.clr-segmented label {
    flex-grow: 1;
    padding: 4px 0;
    text-align: center;
    cursor: pointer;
}

.clr-segmented label:first-of-type {
    border-radius: 10px 0 0 10px;
}

.clr-segmented label:last-of-type {
    border-radius: 0 10px 10px 0;
}

.clr-segmented input:checked+label {
    color: #fff;
    background-color: #666;
}

.clr-swatches {
    order: 2;
    width: calc(100% - 32px);
    margin: 0 16px;
}

.clr-swatches div {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 12px;
    justify-content: center;
}

.clr-swatches button {
    position: relative;
    width: 20px;
    height: 20px;
    margin: 0 4px 6px 4px;
    border: 0;
    border-radius: 50%;
    color: inherit;
    text-indent: -1000px;
    white-space: nowrap;
    overflow: hidden;
    cursor: pointer;
}

.clr-swatches button:after {
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: inherit;
    background-color: currentColor;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .1);
}

input.clr-color {
    order: 1;
    width: calc(100% - 80px);
    height: 32px;
    margin: 15px 20px 20px 0;
    padding: 0 10px;
    border: 1px solid #ddd;
    border-radius: 16px;
    color: #444;
    background-color: #fff;
    font-family: sans-serif;
    font-size: 14px;
    text-align: center;
    box-shadow: none;
}

input.clr-color:focus {
    outline: none;
    border: 1px solid #1e90ff;
}

.made-in-select>.select2 select2-container select2-container--bootstrap4 {
    width: 300px !important;
}

.city_list_select>.select2 select2-container select2-container--bootstrap4 {
    width: 494.6600000000001px !important;
}

.zipcode_list_select>.select2 select2-container select2-container--bootstrap4 {
    width: 494.6600000000001px !important;
}

.tagify__input {
    flex-grow: 1;
    display: inline-block;
    min-width: 110px;
    margin: 5px;
    padding: 0.3em 0.5em;
    padding: var(--tag-pad, .3em .5em);
    line-height: inherit;
    position: relative;
    white-space: pre-wrap;
    color: inherit;
    color: var(--input-color, inherit);
    overflow: hidden;
    box-sizing: inherit;
}

.clr-clear {
    display: none;
    order: 2;
    height: 24px;
    margin: 0 20px 20px auto;
    padding: 0 20px;
    border: 0;
    border-radius: 12px;
    color: #fff;
    background-color: #666;
    font-family: inherit;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
}

.clr-preview {
    position: relative;
    width: 32px;
    height: 32px;
    margin: 15px 0 20px 20px;
    border: 0;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
}

.clr-preview:before,
.clr-preview:after {
    content: '';
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    border: 1px solid #fff;
    border-radius: 50%;
}

.clr-preview:after {
    border: 0;
    background-color: currentColor;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, .1);
}

.clr-marker,
.clr-hue div,
.clr-alpha div,
.clr-color {
    box-sizing: border-box;
}

.clr-field {
    display: inline-block;
    position: relative;
    color: transparent;
}

.clr-field button {
    position: absolute;
    width: 30px;
    height: 100%;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    border: 0;
    color: inherit;
    text-indent: -1000px;
    white-space: nowrap;
    overflow: hidden;
    pointer-events: none;
}

.clr-field button:after {
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: inherit;
    background-color: currentColor;
    box-shadow: inset 0 0 1px rgba(0, 0, 0, .5);
}

.clr-alpha,
.clr-alpha div,
.clr-swatches button,
.clr-preview:before,
.clr-field button {
    background-image: repeating-linear-gradient(45deg, #aaa 25%, transparent 25%, transparent 75%, #aaa 75%, #aaa), repeating-linear-gradient(45deg, #aaa 25%, #fff 25%, #fff 75%, #aaa 75%, #aaa);
    background-position: 0 0, 4px 4px;
    background-size: 8px 8px;
}

.clr-marker:focus {
    outline: none;
}

.clr-keyboard-nav .clr-marker:focus,
.clr-keyboard-nav .clr-hue input:focus+div,
.clr-keyboard-nav .clr-alpha input:focus+div,
.clr-keyboard-nav .clr-segmented input:focus+label {
    outline: none;
    box-shadow: 0 0 0 2px #1e90ff, 0 0 2px 2px #fff;
}


/* Dark theme */

.clr-dark {
    background-color: #444;
}

.clr-dark .clr-segmented {
    border-color: #777;
}

.clr-dark .clr-swatches button:after {
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, .3);
}

.clr-dark input.clr-color {
    color: #fff;
    border-color: #777;
    background-color: #555;
}

.clr-dark input.clr-color:focus {
    border-color: #1e90ff;
}

.clr-dark .clr-preview:after {
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, .5);
}


/* Polaroid theme */

.clr-picker.clr-polaroid {
    border-radius: 6px;
    box-shadow: 0 0 5px rgba(0, 0, 0, .1), 0 5px 30px rgba(0, 0, 0, .2);
}

.clr-picker.clr-polaroid:before {
    content: '';
    display: block;
    position: absolute;
    width: 16px;
    height: 10px;
    left: 20px;
    top: -10px;
    border: solid transparent;
    border-width: 0 8px 10px 8px;
    border-bottom-color: currentColor;
    box-sizing: border-box;
    color: #fff;
    filter: drop-shadow(0 -4px 3px rgba(0, 0, 0, .1));
    pointer-events: none;
}

.clr-picker.clr-polaroid.clr-dark:before {
    color: #444;
}

.clr-picker.clr-polaroid.clr-left:before {
    left: auto;
    right: 20px;
}

.clr-picker.clr-polaroid.clr-top:before {
    top: auto;
    bottom: -10px;
    transform: rotateZ(180deg);
}

.clr-polaroid .clr-gradient {
    width: calc(100% - 20px);
    height: 120px;
    margin: 10px;
    border-radius: 3px;
}

.clr-polaroid .clr-hue,
.clr-polaroid .clr-alpha {
    width: calc(100% - 30px);
    height: 10px;
    margin: 6px 15px;
    border-radius: 5px;
}

.clr-polaroid .clr-hue div,
.clr-polaroid .clr-alpha div {
    box-shadow: 0 0 5px rgba(0, 0, 0, .2);
}

.clr-polaroid .clr-format {
    width: calc(100% - 20px);
    margin: 0 10px 15px;
}

.clr-polaroid .clr-swatches {
    width: calc(100% - 12px);
    margin: 0 6px;
}

.clr-polaroid .clr-swatches div {
    padding-bottom: 10px;
}

.clr-polaroid .clr-swatches button {
    width: 22px;
    height: 22px;
}

.clr-polaroid input.clr-color {
    width: calc(100% - 60px);
    margin: 10px 10px 15px 0;
}

.clr-polaroid .clr-clear {
    margin: 0 10px 15px auto;
}

.clr-polaroid .clr-preview {
    margin: 10px 0 15px 10px;
}


/* Large theme */

.clr-picker.clr-large {
    width: 275px;
}

.clr-large .clr-gradient {
    height: 150px;
}

.clr-large .clr-swatches button {
    width: 22px;
    height: 22px;
}

.image-box-100,
.image-box-100 a {
    width: 100px;
    height: 100px;
    display: flex;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: center;
    align-items: center;
}

.image-box-100 img {
    max-width: 100%;
    max-height: 100%;
}


/* chat css */

.chat-hide-show {
    display: none !important;
}

.chat-min {
    height: 73vh !important;
}

.chat-max {
    height: 88vh !important;
}

.chat-theme-dark {
    background-color: #1A1D21 !important;
    border-top: 2px solid #17a2b8 !important;
}

.chat-theme-light {
    background-color: #F8F8FA !important;
    border-top: 2px solid #383F45 !important;
}

.chat-theme-light .chat-left .person-group-chat {
    background: #8e8e8e;
    padding: 1px 5px;
    border-radius: 3px;
    position: absolute;
    color: #ececec;
    top: -22px;
    left: 0;
    font-size: 12px;
}

.chat-theme-light .chat-right .person-group-chat {
    background: #8e8e8e;
    padding: 1px 5px;
    border-radius: 3px;
    position: absolute;
    color: #ececec;
    top: -22px;
    right: 0;
    font-size: 12px;
}

.chat-avtar-search {
    border-radius: 50%;
    border: none;
    width: 50px;
    height: 50px;
    background: #99e2ff;
    text-align: center;
    line-height: 52px;
    font-size: 26px;
    font-weight: 800;
    color: #777777;
    margin: 0px 16px 0px 0px;
}

.chat-avtar-main {
    border-radius: 50%;
    border: none;
    width: 45px;
    height: 45px;
    background: #e1ebff;
    text-align: center;
    line-height: 52px;
    font-size: 26px;
    font-weight: 800;
    color: #777777;
}

.chat-box .chat-content .chat-item>.chat-avtar {
    float: left;
    /*    border-radius: 50%;
    border: none;
    width: 50px;
    height: 50px;
    background: #e1ebff;
    text-align: center;
    line-height: 52px;
    font-size: 26px;
    font-weight: 800;
    color: #777777;*/
    -webkit-user-drag: none !important;
}

.chat-box .chat-content .chat-item.chat-right .chat-avtar {
    float: right;
    /*    border-radius: 50%;
    border: none;
    width: 50px;
    height: 50px;
    background: #ffe1e1;
    text-align: center;
    line-height: 52px;
    font-size: 26px;
    font-weight: 800;
    color: #777777;*/
    -webkit-user-drag: none;
}

.chat-theme-light .delete-msg {
    position: absolute;
    color: white;
    top: 0;
    right: 3px;
}

.chat-theme-dark .delete-msg {
    position: absolute;
    color: white;
    top: 0;
    right: 3px;
}

.chat-theme-light .download-msg {
    position: absolute;
    color: black;
    top: 0;
    left: 3px;
}

.chat-theme-dark .download-msg {
    position: absolute;
    color: black;
    top: 0;
    left: 3px;
}

.chat_media_img {
    grid-area: img;
}

.chat_media_file {
    grid-area: file;
    color: #b13c3c;
}

.chat_media_size {
    grid-area: size;
}


.chat-theme-light .chat-files {
    text-align: center;
    display: grid;
    grid-template-areas:
        'img file file file size size';
    grid-gap: 10px;
    background-color: #cccccc;
    border-radius: 3px;
    padding: 10px;
    color: black;
    margin: 1px;
}

.chat-theme-dark .chat-files {
    text-align: center;
    display: grid;
    grid-template-areas:
        'img file file file size size';
    grid-gap: 10px;
    background-color: #cccccc;
    border-radius: 3px;
    padding: 10px;
    color: black;
    margin: 1px;
}

.chat-files-search {
    text-align: center;
    display: grid;
    grid-template-areas:
        'img file file file size fa-download';
    grid-gap: 10px;
    background-color: #cccccc;
    border-radius: 3px;
    padding: 10px;
    color: black;
    margin: 1px;
}

.chat-theme-light .chat-image-view {
    position: relative;
    background-color: #cccccc;
    border-radius: 3px;
    padding: 5px;
    color: black;
    margin: 1px;
}


.chat-theme-light .msg_text_media {
    display: grid;
}

.chat-theme-dark .chat-image-view {
    position: relative;
    background-color: #cccccc;
    border-radius: 3px;
    padding: 5px;
    color: black;
    margin: 1px;
}

.chat-theme-dark .msg_text_media {
    display: grid;
}

.chat-theme-light .download-btn-styling {
    background: #27ab45;
    color: black;
    padding: 7px;
    border-radius: 3px;
    display: none;
    margin: 8px 5px 0px 0px;
}

.chat-theme-dark .download-btn-styling {
    background: #27ab45;
    color: black;
    padding: 7px;
    border-radius: 3px;
    display: none;
    margin: 8px 5px 0px 0px;
}

.chat-image-view:hover .download-btn-styling {
    display: block;
}

.chat-files:hover .download-btn-styling {
    display: block;
}


.chat-theme-light .go-to-bottom-btn {
    cursor: pointer;
    padding: 6px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    color: white !important;
    border: none;
    position: absolute;
    top: -48%;
    right: 0px;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    box-shadow: 0px 4px 7px 4px #00000036;
    display: none;
    z-index: 2;
}

.chat-theme-dark .go-to-bottom-btn {
    padding: 3px;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    color: white !important;
    border: none;
    position: absolute;
    top: -48%;
    right: 0px;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.03);
    display: none;
}

.chat-theme-dark .chat-preview-btn {
    position: absolute;
    top: 30%;
    right: 60px;
    color: #868686 !important;
}

.chat-theme-light .chat-preview-btn {
    position: absolute;
    top: 30%;
    right: 60px;
    color: #414141 !important;
}

.chat-theme-dark .chat-preview-btn:hover {
    color: #5a5a5a !important;
}

.chat-theme-light .chat-preview-btn:hover {
    color: #303030 !important;
}

.chat-theme-dark .chat-time {
    color: #d8d8d8 !important;
}

.chat-theme-light .new-msg-rcv {
    font-weight: 1000 !important;
    color: #383F45 !important;
}

.chat-theme-dark .new-msg-rcv {
    font-weight: 1000 !important;
    color: #FFFFFF !important;
}

.chat-theme-light .chat-bg {
    background-image: url(./../img/bg-chat.jpg) !important;
}

.chat-theme-light .text-successg {
    color: #39E500 !important;
}

.chat-theme-dark .chat-bg {
    background-color: #303335 !important;
}

.chat-theme-dark .text-success {
    color: #39E500 !important;
}

.chat-theme-dark .chat-search-box {
    background-color: #1a1d21 !important;
    border: 1px solid #a6a7ab !important;
    border-radius: .25rem !important;
    margin-right: 8px !important;
    height: 30px !important;
    width: -webkit-fill-available;
}

.chat-theme-dark .chat-search-box:hover {
    background-color: #363b42 !important;
    border: 1px solid #c9cacc !important;
}

.chat-theme-dark .chat-search-box:focus {
    background-color: #363b42 !important;
    border: 1px solid #c9cacc !important;
    color: #c9cacc !important;
}

.chat-theme-light .chat-search-box {
    border-radius: .25rem !important;
    margin-right: 8px !important;
    height: 30px !important;
    width: -webkit-fill-available;
}

.chat-theme-light .chat-search-box:hover {
    background-color: #f2f2f7;
    border-color: #d9dae4;
}

.chat-theme-dark .chat-search-btn {
    background-color: #1a1d21 !important;
    border-color: #a6a7ab !important;
}

.chat-scroll {
    overflow: scroll !important;
    outline: none !important;
}

/* width */
.chat-scroll::-webkit-scrollbar {
    width: 7px;
}

/* Track */
.chat-scroll::-webkit-scrollbar-track {
    border-radius: 7px;
}

/* Handle */
.chat-scroll::-webkit-scrollbar-thumb {
    background: rgb(66, 66, 66);
    border: 1px solid rgb(255, 255, 255);
    border-radius: 5px;
}

.chat-theme-dark .active {
    font-weight: 700 !important;
    background: #3abaf4;
    padding: 3px 15px;
    color: #FFFDF9 !important;
}

.chat-theme-dark .active:hover {
    background: #3abaf4 !important;
    color: #FFFDF9 !important;
}

.chat-theme-light .active {
    font-weight: 700 !important;
    background: #3abaf4;
    padding: 3px 15px;
    color: #FFFDF9 !important;
}

.chat-theme-light .active:hover {
    background: #3abaf4 !important;
    color: #FFFDF9 !important;
}

.chat-theme-dark .chat-person {
    font-weight: 700;
    color: #ababab;
    padding: 3px 15px;
}

.chat-theme-dark code {
    background: #e8e8e8;
    padding: 6px 8px;
    border-radius: 4px;
}

.chat-theme-light code {
    background: #e8e8e8;
    padding: 6px 8px;
    border-radius: 4px;
}

.chat-theme-dark .chat-person:hover {
    background: rgb(0, 0, 0);
    cursor: pointer;
}

.chat-theme-light .chat-person {
    font-weight: 700;
    color: #4f5961;
    padding: 3px 15px;
}

.chat-theme-light .chat-person:hover {
    background: #FFFFFF;
    cursor: pointer;
}

.chat-theme-dark .text-color {
    color: #ababab !important;

}

.chat-theme-light .text-color {
    color: #383F45 !important;
}

.chat-theme-dark .text-color h4 {
    color: #ababab !important;
}

.chat-theme-light .text-color h4 {
    color: #383F45 !important;
}

.chat-theme-dark .theme-inputs {
    background-color: #1a1d21 !important;
    border: 1px solid #a6a7ab !important;
    border-radius: 6px !important;
    color: #c9cacc !important;
    height: auto !important;
    white-space: pre-wrap !important;
}

.chat-theme-light .theme-inputs {
    border: 1px solid #383F45 !important;
    border-radius: 6px !important;
    height: auto !important;
    white-space: pre-wrap !important;
}

.chat-card-header {
    line-height: 9px !important;
    min-height: 0px !important;
    padding: 5px 8px !important;
    border-bottom: 0px !important;
}

.chat-card-header h4 {
    font-size: 17px !important;
}

.chat-list-unstyled-border li {
    border-bottom: 0px !important;
    padding-bottom: 0px !important;
    margin-bottom: 0px !important;
}

.chat-card-body {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    flex: 0 !important;
}

.chat-img-undrag {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-drag: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}


.chat_divider {
    padding: 8px 10px;
    text-align: center;
    font-size: medium;
    color: brown;
    margin: 0 0 20px 0px;
    display: flex;
    align-items: center;
    text-align: center;
}

.chat_divider::before,
.chat_divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #cf9a5e;
}

.chat_divider::before {
    margin-right: .25em;
}

.chat_divider::after {
    margin-left: .25em;
}


.chat_loader {
    padding: 8px 10px;
    text-align: center;
    font-size: medium;
    color: brown;
    margin: 0 0 20px 0px;
    display: flex;
    align-items: center;
    text-align: center;
    display: none;
}

.chat_loader::before,
.chat_loader::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #cf9a5e;
}

.chat_loader::before {
    margin-right: .25em;
}

.chat_loader::after {
    margin-left: .25em;
}


#chat-input-textarea-result {
    background-color: rgba(117, 117, 117, 0.36);
    position: absolute;
    bottom: 51px;
    border: 1.5px dashed rgb(119, 122, 125) !important;
    border-radius: 6px !important;
    height: auto;
}

.badge-chat {
    vertical-align: middle;
    border-radius: 5px;
    float: right;
    background-color: #fc544b;
    color: white;
    /* display: inline-block; if you get any error from this class then first uncmnt this and check */
    padding: .30em 1em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    margin-top: 2px;
}

.loading-message {
    font-size: 15px;
    padding: 20px;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}


.firebase_config .sms_gateway>p {
    color: red;
}

.uploaded_image_here {
    height: 120px;
    width: 120px;
}

.firebase_icon {
    height: 20px;
}

.invoice_logo {
    max-width: 100px;
    max-height: 70px;
}

.system_health_table table {
    font-family: arial, sans-serif;
    border-collapse: collapse;
    width: 100%;
}

.system_health_table td,
.system_health_table th {
    border: 1px solid #cecece;
    text-align: left;
    padding: 8px;
}

.system_health_table tr:nth-child(even) {
    background-color: #dddddd;
}

/* Custom responsive class for the table container */
.custom-table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    /* For smooth scrolling on mobile */
}

/* Default width for the table (full width on small screens) */
.custom-table-responsive {
    max-width: 1000px;
}

.isDisabled {
    color: currentColor;
    cursor: not-allowed;
    opacity: 0.5;
    text-decoration: none;
}


.attachment_image {
    max-width: 100%;
    height: 90px;
    margin: 10px;
}