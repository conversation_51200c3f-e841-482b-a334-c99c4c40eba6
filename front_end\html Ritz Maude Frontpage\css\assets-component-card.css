.card-wrapper{color:inherit;height:100%;position:relative;text-decoration:none}.card{text-decoration:none;text-align:var(--text-alignment)}.card:not(.ratio){display:flex;flex-direction:column;height:100%}.card.card--horizontal{--text-alignment: left;--image-padding: 0rem;flex-direction:row;align-items:flex-start;gap:1.5rem}.card--horizontal.ratio:before{padding-bottom:0}.card--card.card--horizontal{padding:1.2rem}.card--card.card--horizontal.card--text{column-gap:0}.card--card{height:100%}.card--card,.card--standard .card__inner{position:relative;box-sizing:border-box;border-radius:var(--border-radius);border:var(--border-width) solid rgba(var(--color-foreground),var(--border-opacity))}.card--card:after,.card--standard .card__inner:after{content:"";position:absolute;z-index:-1;width:calc(var(--border-width) * 2 + 100%);height:calc(var(--border-width) * 2 + 100%);top:calc(var(--border-width) * -1);left:calc(var(--border-width) * -1);border-radius:var(--border-radius);box-shadow:var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius) rgba(var(--color-shadow),var(--shadow-opacity))}.card--card.gradient,.card__inner.gradient{transform:perspective(0)}.card__inner.color-scheme-1{background:transparent}.card .card__inner .card__media{overflow:hidden;z-index:0;border-radius:calc(var(--border-radius) - var(--border-width) - var(--image-padding))}.card--card .card__inner .card__media{border-bottom-right-radius:0;border-bottom-left-radius:0}.card--standard.card--text{background-color:transparent}.card-information{text-align:var(--text-alignment)}.card__media,.card .media{bottom:0;position:absolute;top:0}.card .media{width:100%}.card__media{margin:var(--image-padding);width:calc(100% - 2 * var(--image-padding))}.card--standard .card__media{margin:var(--image-padding)}.card__inner{width:100%}.card--media .card__inner .card__content{position:relative;padding:calc(var(--image-padding) + 1rem)}.card__content{display:grid;grid-template-rows:minmax(0,1fr) max-content minmax(0,1fr);padding:1rem;width:100%;flex-grow:1}.card__content--auto-margins{grid-template-rows:minmax(0,auto) max-content minmax(0,auto)}.card__information{grid-row-start:2;padding:1.3rem 1rem}.card:not(.ratio)>.card__content{grid-template-rows:max-content minmax(0,1fr) max-content auto}.card-information .card__information-volume-pricing-note{margin-top:.6rem;line-height:calc(.5 + .4 / var(--font-body-scale));color:rgba(var(--color-foreground),.75)}@media screen and (min-width: 750px){.card__information{padding-bottom:1.7rem;padding-top:1.7rem}}.card__badge{align-self:flex-end;grid-row-start:3;justify-self:flex-start}.card__badge.top{align-self:flex-start;grid-row-start:1}.card__badge.right{justify-self:flex-end}.card:not(.card--horizontal)>.card__content>.card__badge{margin:1.3rem}.card__media .media img{height:100%;object-fit:cover;object-position:center center;width:100%}.card__inner:not(.ratio)>.card__content{height:100%}.card__heading{margin-top:0;margin-bottom:0}.card__heading:last-child{margin-bottom:0}.card--horizontal .card__heading,.card--horizontal .price__container .price-item,.card--horizontal__quick-add{font-size:calc(var(--font-heading-scale) * 1.2rem)}.card--horizontal .card-information>*:not(.visually-hidden:first-child)+*:not(.rating):not(.card__information-volume-pricing-note){margin-top:0}.card--horizontal__quick-add:before{box-shadow:none}@media only screen and (min-width: 750px){.card--horizontal .card__heading,.card--horizontal .price__container .price-item,.card--horizontal__quick-add{font-size:calc(var(--font-heading-scale) * 1.3rem)}}.card--card.card--media>.card__content{margin-top:calc(0rem - var(--image-padding))}.card--standard.card--text a:after,.card--card .card__heading a:after{bottom:calc(var(--border-width) * -1);left:calc(var(--border-width) * -1);right:calc(var(--border-width) * -1);top:calc(var(--border-width) * -1)}.card__heading a:after{bottom:0;content:"";left:0;position:absolute;right:0;top:0;z-index:1}.card__heading a:after{outline-offset:.3rem}.card__heading a:focus:after{box-shadow:0 0 0 .3rem rgb(var(--color-background)),0 0 .5rem .4rem rgba(var(--color-foreground),.3);outline:.2rem solid rgba(var(--color-foreground),.5)}.card__heading a:focus-visible:after{box-shadow:0 0 0 .3rem rgb(var(--color-background)),0 0 .5rem .4rem rgba(var(--color-foreground),.3);outline:.2rem solid rgba(var(--color-foreground),.5)}.card__heading a:focus:not(:focus-visible):after{box-shadow:none;outline:0}.card__heading a:focus{box-shadow:none;outline:0}@media screen and (min-width: 990px){.card .media.media--hover-effect>img:only-child,.card-wrapper .media.media--hover-effect>img:only-child{transition:transform var(--duration-long) ease}.card:hover .media.media--hover-effect>img:first-child:only-child,.card-wrapper:hover .media.media--hover-effect>img:first-child:only-child{transform:scale(1.03)}.card-wrapper:hover .media.media--hover-effect>img:first-child:not(:only-child){opacity:0}.card-wrapper:hover .media.media--hover-effect>img+img{opacity:1;transition:transform var(--duration-long) ease;transform:scale(1.03)}.underline-links-hover:hover a{text-decoration:underline;text-underline-offset:.3rem}}.card--standard.card--media .card__inner .card__information,.card--standard.card--text:not(.card--horizontal)>.card__content .card__heading:not(.card__heading--placeholder),.card--standard:not(.card--horizontal)>.card__content .card__badge,.card--standard.card--text.article-card>.card__content .card__information,.card--standard>.card__content .card__caption{display:none}.card--standard:not(.card--horizontal) .placeholder-svg{width:100%}.card--standard>.card__content{padding:0}.card--standard>.card__content .card__information{padding-left:0;padding-right:0}.card--card.card--media .card__inner .card__information,.card--card.card--text .card__inner,.card--card.card--media>.card__content .card__badge{display:none}.card--horizontal .card__badge,.card--horizontal.card--text .card__inner{display:none}.card--extend-height{height:100%}.card--extend-height.card--standard.card--text,.card--extend-height.card--media{display:flex;flex-direction:column}.card--extend-height.card--standard.card--text .card__inner,.card--extend-height.card--media .card__inner{flex-grow:1}.card .icon-wrap{margin-left:.8rem;white-space:nowrap;transition:transform var(--duration-short) ease;overflow:hidden}.card-information>*+*{margin-top:.5rem}.card-information{width:100%}.card-information>*{line-height:calc(1 + .4 / var(--font-body-scale));color:rgb(var(--color-foreground))}.card-information>.price{color:rgb(var(--color-foreground))}.card--horizontal .card-information>.price{color:rgba(var(--color-foreground),.75)}.card-information>.rating{margin-top:.4rem}.card-information>*:not(.visually-hidden:first-child)+*:not(.rating):not(.card__information-volume-pricing-note){margin-top:.7rem}.card-information .caption{letter-spacing:.07rem}.card-article-info{margin-top:1rem}.card--shape .card__content{padding-top:0}.card--shape.card--standard:not(.card--text) .card__inner{border:0;background-color:transparent;filter:drop-shadow(var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius) rgba(var(--color-shadow),var(--shadow-opacity)))}.card--shape.card--standard:not(.card--text) .card__inner:after{display:none}.grid__item:nth-child(2n) .shape--blob{clip-path:polygon(var(--shape--blob-2))}.grid__item:nth-child(3n) .shape--blob{clip-path:polygon(var(--shape--blob-3))}.grid__item:nth-child(4n) .shape--blob{clip-path:polygon(var(--shape--blob-4))}.grid__item:nth-child(5n) .shape--blob{clip-path:polygon(var(--shape--blob-5))}.grid__item:nth-child(7n) .shape--blob{clip-path:polygon(var(--shape--blob-6))}.grid__item:nth-child(8n) .shape--blob{clip-path:polygon(var(--shape--blob-1))}@media (prefers-reduced-motion: no-preference){.product-card-wrapper .shape--round{transition:clip-path var(--duration-long) ease}.product-card-wrapper:hover .shape--round{clip-path:ellipse(47% 47% at 50% 50%)}.product-card-wrapper .shape--blob{transition:clip-path var(--duration-long) ease-in-out}.product-card-wrapper:hover .shape--blob{clip-path:polygon(var(--shape--blob-5))}.grid__item:nth-child(2n) .product-card-wrapper:hover .shape--blob{clip-path:polygon(var(--shape--blob-6))}.grid__item:nth-child(3n) .product-card-wrapper:hover .shape--blob{clip-path:polygon(var(--shape--blob-1))}.grid__item:nth-child(4n) .product-card-wrapper:hover .shape--blob{clip-path:polygon(var(--shape--blob-2))}.grid__item:nth-child(5n) .product-card-wrapper:hover .shape--blob{clip-path:polygon(var(--shape--blob-3))}.grid__item:nth-child(7n) .product-card-wrapper:hover .shape--blob{clip-path:polygon(var(--shape--blob-4))}.grid__item:nth-child(8n) .product-card-wrapper:hover .shape--blob{clip-path:polygon(var(--shape--blob-5))}}
/*# sourceMappingURL=/cdn/shop/t/1/assets/component-card.css.map?v=170127402091165654191707074525 */
